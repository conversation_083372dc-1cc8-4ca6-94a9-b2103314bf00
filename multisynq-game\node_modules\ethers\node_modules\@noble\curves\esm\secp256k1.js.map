{"version": 3, "file": "secp256k1.js", "sourceRoot": "", "sources": ["../src/secp256k1.ts"], "names": [], "mappings": "AAAA,sEAAsE;AACtE,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAA8B,mBAAmB,EAAE,MAAM,2BAA2B,CAAC;AAE5F,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AACjG,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,6BAA6B,CAAC;AACvE,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AAEjD,MAAM,UAAU,GAAG,MAAM,CAAC,oEAAoE,CAAC,CAAC;AAChG,MAAM,UAAU,GAAG,MAAM,CAAC,oEAAoE,CAAC,CAAC;AAChG,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAM,UAAU,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AAE/D;;;GAGG;AACH,SAAS,OAAO,CAAC,CAAS;IACxB,MAAM,CAAC,GAAG,UAAU,CAAC;IACrB,kBAAkB;IAClB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;IAC7E,kBAAkB;IAClB,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;IAC9D,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU;IACtC,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;IACpC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IACvC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IACvC,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IACxC,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5C,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7C,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IACvC,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC9B,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IACzE,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,EAAE,GAAG,KAAK,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;AAEtE,MAAM,CAAC,MAAM,SAAS,GAAG,WAAW,CAClC;IACE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,EAAE;IACF,CAAC,EAAE,UAAU;IACb,wCAAwC;IACxC,EAAE,EAAE,MAAM,CAAC,+EAA+E,CAAC;IAC3F,EAAE,EAAE,MAAM,CAAC,+EAA+E,CAAC;IAC3F,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,IAAI,EAAE,IAAI;IACV;;;;;OAKG;IACH,IAAI,EAAE;QACJ,IAAI,EAAE,MAAM,CAAC,oEAAoE,CAAC;QAClF,WAAW,EAAE,CAAC,CAAS,EAAE,EAAE;YACzB,MAAM,CAAC,GAAG,UAAU,CAAC;YACrB,MAAM,EAAE,GAAG,MAAM,CAAC,oCAAoC,CAAC,CAAC;YACxD,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,oCAAoC,CAAC,CAAC;YAC/D,MAAM,EAAE,GAAG,MAAM,CAAC,qCAAqC,CAAC,CAAC;YACzD,MAAM,EAAE,GAAG,EAAE,CAAC;YACd,MAAM,SAAS,GAAG,MAAM,CAAC,qCAAqC,CAAC,CAAC,CAAC,0BAA0B;YAE3F,MAAM,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YACjC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YAClC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;YACvC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;YACpC,MAAM,KAAK,GAAG,EAAE,GAAG,SAAS,CAAC;YAC7B,MAAM,KAAK,GAAG,EAAE,GAAG,SAAS,CAAC;YAC7B,IAAI,KAAK;gBAAE,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;YACvB,IAAI,KAAK;gBAAE,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;YACvB,IAAI,EAAE,GAAG,SAAS,IAAI,EAAE,GAAG,SAAS,EAAE;gBACpC,MAAM,IAAI,KAAK,CAAC,sCAAsC,GAAG,CAAC,CAAC,CAAC;aAC7D;YACD,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;QAClC,CAAC;KACF;CACF,EACD,MAAM,CACP,CAAC;AAEF,+FAA+F;AAC/F,iEAAiE;AACjE,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAM,EAAE,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;AAC7E,MAAM,EAAE,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;AAC7E,wFAAwF;AACxF,MAAM,oBAAoB,GAAkC,EAAE,CAAC;AAC/D,SAAS,UAAU,CAAC,GAAW,EAAE,GAAG,QAAsB;IACxD,IAAI,IAAI,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC;IACrC,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,IAAI,GAAG,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC/B,oBAAoB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;KAClC;IACD,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;AAChD,CAAC;AAED,oFAAoF;AACpF,MAAM,YAAY,GAAG,CAAC,KAAwB,EAAE,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACnF,MAAM,QAAQ,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACvD,MAAM,IAAI,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAC/C,MAAM,IAAI,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAC/C,MAAM,KAAK,GAAG,SAAS,CAAC,eAAe,CAAC;AACxC,MAAM,OAAO,GAAG,CAAC,CAAoB,EAAE,CAAS,EAAE,CAAS,EAAE,EAAE,CAC7D,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAE3C,oCAAoC;AACpC,SAAS,mBAAmB,CAAC,IAAa;IACxC,IAAI,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,yCAAyC;IAChG,IAAI,CAAC,GAAG,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,4CAA4C;IAC9E,MAAM,MAAM,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7C,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;AACpD,CAAC;AACD;;;GAGG;AACH,SAAS,MAAM,CAAC,CAAS;IACvB,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAC,iBAAiB;IACvE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACvB,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB;IAC5D,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B;IAC/C,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG;QAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mDAAmD;IACtF,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,mDAAmD;IACnF,CAAC,CAAC,cAAc,EAAE,CAAC;IACnB,OAAO,CAAC,CAAC;AACX,CAAC;AACD;;GAEG;AACH,SAAS,SAAS,CAAC,GAAG,IAAkB;IACtC,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,mBAAmB,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,UAAe;IAC1C,OAAO,mBAAmB,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,oDAAoD;AACpG,CAAC;AAED;;;GAGG;AACH,SAAS,WAAW,CAClB,OAAY,EACZ,UAAmB,EACnB,UAAe,WAAW,CAAC,EAAE,CAAC;IAE9B,MAAM,CAAC,GAAG,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC1C,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC,gCAAgC;IAClG,MAAM,CAAC,GAAG,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,2CAA2C;IAC1F,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,eAAe,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,yDAAyD;IAChI,MAAM,IAAI,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,4CAA4C;IAChG,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,2BAA2B;IACnE,IAAI,EAAE,KAAK,GAAG;QAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAC,kBAAkB;IAC7E,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB;IAC1E,MAAM,CAAC,GAAG,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,gEAAgE;IAChG,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,+CAA+C;IAC/E,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACf,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACvC,iEAAiE;IACjE,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACpF,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;GAGG;AACH,SAAS,aAAa,CAAC,SAAc,EAAE,OAAY,EAAE,SAAc;IACjE,MAAM,GAAG,GAAG,WAAW,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;IACpD,MAAM,CAAC,GAAG,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC1C,MAAM,GAAG,GAAG,WAAW,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;IACpD,IAAI;QACF,MAAM,CAAC,GAAG,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,0CAA0C;QAClF,MAAM,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,yCAAyC;QACzF,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QACzB,MAAM,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,0CAA0C;QAC3F,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QACzB,MAAM,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,0CAA0C;QAChG,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB;QACnD,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC,CAAC,gBAAgB;QAC/E,OAAO,IAAI,CAAC,CAAC,yDAAyD;KACvE;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED,MAAM,CAAC,MAAM,OAAO,GAAG,eAAe,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IAC7C,YAAY,EAAE,mBAAmB;IACjC,IAAI,EAAE,WAAW;IACjB,MAAM,EAAE,aAAa;IACrB,KAAK,EAAE;QACL,gBAAgB,EAAE,SAAS,CAAC,KAAK,CAAC,gBAAgB;QAClD,MAAM;QACN,YAAY;QACZ,eAAe;QACf,eAAe;QACf,UAAU;QACV,GAAG;KACJ;CACF,CAAC,CAAC,EAAE,CAAC;AAEN,MAAM,MAAM,GAAG,eAAe,CAAC,CAAC,GAAG,EAAE,CACnC,UAAU,CACR,EAAE,EACF;IACE,OAAO;IACP;QACE,oEAAoE;QACpE,mEAAmE;QACnE,oEAAoE;QACpE,oEAAoE;KACrE;IACD,OAAO;IACP;QACE,oEAAoE;QACpE,oEAAoE;QACpE,oEAAoE,EAAE,SAAS;KAChF;IACD,OAAO;IACP;QACE,oEAAoE;QACpE,oEAAoE;QACpE,oEAAoE;QACpE,oEAAoE;KACrE;IACD,OAAO;IACP;QACE,oEAAoE;QACpE,oEAAoE;QACpE,oEAAoE;QACpE,oEAAoE,EAAE,SAAS;KAChF;CACF,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAA6C,CAClF,CAAC,EAAE,CAAC;AACP,MAAM,MAAM,GAAG,eAAe,CAAC,CAAC,GAAG,EAAE,CACnC,mBAAmB,CAAC,EAAE,EAAE;IACtB,CAAC,EAAE,MAAM,CAAC,oEAAoE,CAAC;IAC/E,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC;IACjB,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;CAC5B,CAAC,CAAC,EAAE,CAAC;AACR,MAAM,GAAG,GAAG,eAAe,CAAC,CAAC,GAAG,EAAE,CAChC,YAAY,CACV,SAAS,CAAC,eAAe,EACzB,CAAC,OAAiB,EAAE,EAAE;IACpB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,OAAO,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACtB,CAAC,EACD;IACE,GAAG,EAAE,gCAAgC;IACrC,SAAS,EAAE,gCAAgC;IAC3C,CAAC,EAAE,EAAE,CAAC,KAAK;IACX,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,GAAG;IACN,MAAM,EAAE,KAAK;IACb,IAAI,EAAE,MAAM;CACb,CACF,CAAC,EAAE,CAAC;AACP,MAAM,CAAC,MAAM,WAAW,GAAG,eAAe,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;AACrE,MAAM,CAAC,MAAM,aAAa,GAAG,eAAe,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC"}