{"version": 3, "file": "transaction.js", "sourceRoot": "", "sources": ["../src/transaction.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,aAAa,EAAE,GAAG,EAAE,MAAM,aAAa,CAAC;AAEjD,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAErD,OAAO,EAAE,IAAI,IAAI,SAAS,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAEzD,YAAY;AACZ;IAAiC,+BAAS;IAWxC;;;;;;OAMG;IACH,qBAAmB,kBAAsC,EAAE,GAAS;QAApE,YACE,kBAAM,kBAAkB,CAAC,SAY1B;QA7BO,mBAAa,GAAiB,EAAE,CAAC;QAEzC;;WAEG;QACc,UAAI,GAAS,aAAa,EAAqB,CAAC;QAc/D,IAAI,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;YAC1B,KAAI,CAAC,IAAI,GAAG,GAAU,CAAC;SACxB;QAED,KAAI,CAAC,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QAEnE,KAAI,CAAC,QAAQ,GAAG,kBAAkB,CAAC,OAAO,CAAC;QAE3C,oFAAoF;QACpF,KAAI,CAAC,WAAW,GAAG,KAAI,CAAC;;IAC1B,CAAC;IAED;;OAEG;IACI,6BAAO,GAAd,UAAe,IAAY;QACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED;;;OAGG;IACI,sCAAgB,GAAvB,UAAwB,MAAqB;QAArB,uBAAA,EAAA,aAAqB;QAC3C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;SAC9C;QACD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,qCAAe,GAAtB,UAAuB,YAA0B;QAC/C,IAAI,CAAC,aAAa,gBAAQ,YAAY,CAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,4BAAM,GAAb,UAAc,YAAqB;QAAnC,iBAmDC;QAlDC,yEAAyE;QACzE,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE;YACnC,OAAO,SAAS,CAAC;SAClB;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACd,MAAM,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;YACnF,IAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC;SACvC;QAED,8BAA8B;QAC9B,iBAAM,MAAM,YAAC,YAAY,CAAC,CAAC;QAE3B,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;YACzB,0EAA0E;YAC1E,MAAM,CAAC,GAAG,CAAC,kFAAkF,CAAC,CAAC;YAC/F,OAAO,SAAS,CAAC;SAClB;QAED,IAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,KAAK,KAAI,IAAI,CAAC,CAAC,YAAY,EAA5B,CAA4B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAEjH,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7C,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,UAAC,IAAe,EAAE,OAAkB;gBAC3E,IAAI,IAAI,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,EAAE;oBAC7C,OAAO,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;iBAClE;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC,YAAY,CAAC;SACjB;QAED,IAAM,WAAW,GAAU;YACzB,QAAQ,EAAE;gBACR,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE;aAC9B;YACD,KAAK,EAAE,aAAa;YACpB,eAAe,EAAE,IAAI,CAAC,cAAc;YACpC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,SAAS,EAAE,IAAI,CAAC,YAAY;YAC5B,WAAW,EAAE,IAAI,CAAC,IAAI;YACtB,IAAI,EAAE,aAAa;SACpB,CAAC;QAEF,IAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;QAEnE,IAAI,eAAe,EAAE;YACnB,MAAM,CAAC,GAAG,CAAC,mDAAmD,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;YAClH,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;SAC/C;QAED,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;IAC7C,CAAC;IACH,kBAAC;AAAD,CAAC,AAlHD,CAAiC,SAAS,GAkHzC", "sourcesContent": ["import { getCurrentHub, Hub } from '@sentry/hub';\nimport { Event, Measurements, Transaction as TransactionInterface, TransactionContext } from '@sentry/types';\nimport { isInstanceOf, logger } from '@sentry/utils';\n\nimport { Span as SpanClass, SpanRecorder } from './span';\n\n/** JSDoc */\nexport class Transaction extends SpanClass implements TransactionInterface {\n  public name: string;\n  private _measurements: Measurements = {};\n\n  /**\n   * The reference to the current hub.\n   */\n  private readonly _hub: Hub = (getCurrentHub() as unknown) as Hub;\n\n  private readonly _trimEnd?: boolean;\n\n  /**\n   * This constructor should never be called manually. Those instrumenting tracing should use\n   * `Sentry.startTransaction()`, and internal methods should use `hub.startTransaction()`.\n   * @internal\n   * @hideconstructor\n   * @hidden\n   */\n  public constructor(transactionContext: TransactionContext, hub?: Hub) {\n    super(transactionContext);\n\n    if (isInstanceOf(hub, Hub)) {\n      this._hub = hub as Hub;\n    }\n\n    this.name = transactionContext.name ? transactionContext.name : '';\n\n    this._trimEnd = transactionContext.trimEnd;\n\n    // this is because transactions are also spans, and spans have a transaction pointer\n    this.transaction = this;\n  }\n\n  /**\n   * JSDoc\n   */\n  public setName(name: string): void {\n    this.name = name;\n  }\n\n  /**\n   * Attaches SpanRecorder to the span itself\n   * @param maxlen maximum number of spans that can be recorded\n   */\n  public initSpanRecorder(maxlen: number = 1000): void {\n    if (!this.spanRecorder) {\n      this.spanRecorder = new SpanRecorder(maxlen);\n    }\n    this.spanRecorder.add(this);\n  }\n\n  /**\n   * Set observed measurements for this transaction.\n   * @hidden\n   */\n  public setMeasurements(measurements: Measurements): void {\n    this._measurements = { ...measurements };\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public finish(endTimestamp?: number): string | undefined {\n    // This transaction is already finished, so we should not flush it again.\n    if (this.endTimestamp !== undefined) {\n      return undefined;\n    }\n\n    if (!this.name) {\n      logger.warn('Transaction has no name, falling back to `<unlabeled transaction>`.');\n      this.name = '<unlabeled transaction>';\n    }\n\n    // just sets the end timestamp\n    super.finish(endTimestamp);\n\n    if (this.sampled !== true) {\n      // At this point if `sampled !== true` we want to discard the transaction.\n      logger.log('[Tracing] Discarding transaction because its trace was not chosen to be sampled.');\n      return undefined;\n    }\n\n    const finishedSpans = this.spanRecorder ? this.spanRecorder.spans.filter(s => s !== this && s.endTimestamp) : [];\n\n    if (this._trimEnd && finishedSpans.length > 0) {\n      this.endTimestamp = finishedSpans.reduce((prev: SpanClass, current: SpanClass) => {\n        if (prev.endTimestamp && current.endTimestamp) {\n          return prev.endTimestamp > current.endTimestamp ? prev : current;\n        }\n        return prev;\n      }).endTimestamp;\n    }\n\n    const transaction: Event = {\n      contexts: {\n        trace: this.getTraceContext(),\n      },\n      spans: finishedSpans,\n      start_timestamp: this.startTimestamp,\n      tags: this.tags,\n      timestamp: this.endTimestamp,\n      transaction: this.name,\n      type: 'transaction',\n    };\n\n    const hasMeasurements = Object.keys(this._measurements).length > 0;\n\n    if (hasMeasurements) {\n      logger.log('[Measurements] Adding measurements to transaction', JSON.stringify(this._measurements, undefined, 2));\n      transaction.measurements = this._measurements;\n    }\n\n    return this._hub.captureEvent(transaction);\n  }\n}\n"]}