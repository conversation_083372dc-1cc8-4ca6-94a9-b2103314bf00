import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const avalanche = /*#__PURE__*/ defineChain({
    id: 43_114,
    name: 'Avalanche',
    nativeCurrency: {
        decimals: 18,
        name: 'Avalanche',
        symbol: 'AVAX',
    },
    rpcUrls: {
        default: { http: ['https://api.avax.network/ext/bc/C/rpc'] },
    },
    blockExplorers: {
        default: {
            name: 'SnowTrace',
            url: 'https://snowtrace.io',
            apiUrl: 'https://api.snowtrace.io',
        },
    },
    contracts: {
        multicall3: {
            address: '0xca11bde05977b3631167028862be2a173976ca11',
            blockCreated: 11907934,
        },
    },
});
//# sourceMappingURL=avalanche.js.map