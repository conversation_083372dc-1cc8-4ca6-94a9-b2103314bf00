{"version": 3, "file": "express.js", "sourceRoot": "", "sources": ["../../src/integrations/express.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AA6CvC;;;;GAIG;AACH;IAiBE;;OAEG;IACH,iBAAmB,OAAmE;QAAnE,wBAAA,EAAA,YAAmE;QAdtF;;WAEG;QACI,SAAI,GAAW,OAAO,CAAC,EAAE,CAAC;QAY/B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC;QAC7C,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACxF,CAAC;IAED;;OAEG;IACI,2BAAS,GAAhB;QACE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,MAAM,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;YAClE,OAAO;SACR;QACD,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrD,CAAC;IAjCD;;OAEG;IACW,UAAE,GAAW,SAAS,CAAC;IA+BvC,cAAC;CAAA,AAnCD,IAmCC;SAnCY,OAAO;AAqCpB;;;;;;;;;;;;;GAaG;AACH,4FAA4F;AAC5F,SAAS,IAAI,CAAC,EAAY,EAAE,MAAc;IACxC,IAAM,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC;IAExB,QAAQ,KAAK,EAAE;QACb,KAAK,CAAC,CAAC,CAAC;YACN,OAAO,UAA8B,GAAY,EAAE,GAA4C;gBAC7F,IAAM,WAAW,GAAG,GAAG,CAAC,oBAAoB,CAAC;gBAC7C,IAAI,WAAW,EAAE;oBACf,IAAM,MAAI,GAAG,WAAW,CAAC,UAAU,CAAC;wBAClC,WAAW,EAAE,EAAE,CAAC,IAAI;wBACpB,EAAE,EAAE,gBAAc,MAAQ;qBAC3B,CAAC,CAAC;oBACH,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE;wBACjB,MAAI,CAAC,MAAM,EAAE,CAAC;oBAChB,CAAC,CAAC,CAAC;iBACJ;gBACD,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YACjC,CAAC,CAAC;SACH;QACD,KAAK,CAAC,CAAC,CAAC;YACN,OAAO,UAEL,GAAY,EACZ,GAA4C,EAC5C,IAAgB;;gBAEhB,IAAM,WAAW,GAAG,GAAG,CAAC,oBAAoB,CAAC;gBAC7C,IAAM,IAAI,SAAG,WAAW,0CAAE,UAAU,CAAC;oBACnC,WAAW,EAAE,EAAE,CAAC,IAAI;oBACpB,EAAE,EAAE,gBAAc,MAAQ;iBAC3B,CAAC,CAAC;gBACH,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;oBAA8B,cAAkB;yBAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;wBAAlB,yBAAkB;;;oBACtE,MAAA,IAAI,0CAAE,MAAM,GAAG;oBACf,IAAI,CAAC,IAAI,OAAT,IAAI,YAAM,IAAI,GAAK,IAAI,GAAE;gBAC3B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;SACH;QACD,KAAK,CAAC,CAAC,CAAC;YACN,OAAO,UAEL,GAAU,EACV,GAAY,EACZ,GAAqC,EACrC,IAAgB;;gBAEhB,IAAM,WAAW,GAAG,GAAG,CAAC,oBAAoB,CAAC;gBAC7C,IAAM,IAAI,SAAG,WAAW,0CAAE,UAAU,CAAC;oBACnC,WAAW,EAAE,EAAE,CAAC,IAAI;oBACpB,EAAE,EAAE,gBAAc,MAAQ;iBAC3B,CAAC,CAAC;gBACH,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;oBAA8B,cAAkB;yBAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;wBAAlB,yBAAkB;;;oBAC3E,MAAA,IAAI,0CAAE,MAAM,GAAG;oBACf,IAAI,CAAC,IAAI,OAAT,IAAI,YAAM,IAAI,GAAK,IAAI,GAAE;gBAC3B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;SACH;QACD,OAAO,CAAC,CAAC;YACP,MAAM,IAAI,KAAK,CAAC,kDAAgD,KAAO,CAAC,CAAC;SAC1E;KACF;AACH,CAAC;AAED;;;;;;;;;GASG;AACH,SAAS,kBAAkB,CAAC,IAAe,EAAE,MAAc;IACzD,OAAO,IAAI,CAAC,GAAG,CAAC,UAAC,GAAY;QAC3B,IAAI,OAAO,GAAG,KAAK,UAAU,EAAE;YAC7B,OAAO,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;SAC1B;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACtB,OAAO,GAAG,CAAC,GAAG,CAAC,UAAC,CAAU;gBACxB,IAAI,OAAO,CAAC,KAAK,UAAU,EAAE;oBAC3B,OAAO,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;iBACxB;gBACD,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,GAAG,CAAC;IACb,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,MAAc,EAAE,MAAc;IACrD,IAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;IAExC,MAAM,CAAC,MAAM,CAAC,GAAG;QAAS,cAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,yBAAkB;;QAC1C,OAAO,gBAAgB,CAAC,IAAI,OAArB,gBAAgB,YAAM,IAAI,GAAK,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,GAAE;IAC1E,CAAC,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAS,qBAAqB,CAAC,MAAc,EAAE,OAAsB;IAAtB,wBAAA,EAAA,YAAsB;IACnE,OAAO,CAAC,OAAO,CAAC,UAAC,MAAc,IAAK,OAAA,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,EAA/B,CAA+B,CAAC,CAAC;AACvE,CAAC", "sourcesContent": ["import { Integration, Transaction } from '@sentry/types';\nimport { logger } from '@sentry/utils';\n\ntype Method =\n  | 'all'\n  | 'get'\n  | 'post'\n  | 'put'\n  | 'delete'\n  | 'patch'\n  | 'options'\n  | 'head'\n  | 'checkout'\n  | 'copy'\n  | 'lock'\n  | 'merge'\n  | 'mkactivity'\n  | 'mkcol'\n  | 'move'\n  | 'm-search'\n  | 'notify'\n  | 'purge'\n  | 'report'\n  | 'search'\n  | 'subscribe'\n  | 'trace'\n  | 'unlock'\n  | 'unsubscribe'\n  | 'use';\n\ntype Router = {\n  [method in Method]: (...args: any) => any; // eslint-disable-line @typescript-eslint/no-explicit-any\n};\n\ninterface ExpressResponse {\n  once(name: string, callback: () => void): void;\n}\n\n/**\n * Internal helper for `__sentry_transaction`\n * @hidden\n */\ninterface SentryTracingResponse {\n  __sentry_transaction?: Transaction;\n}\n\n/**\n * Express integration\n *\n * Provides an request and error handler for Express framework as well as tracing capabilities\n */\nexport class Express implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'Express';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = Express.id;\n\n  /**\n   * Express App instance\n   */\n  private readonly _router?: Router;\n  private readonly _methods?: Method[];\n\n  /**\n   * @inheritDoc\n   */\n  public constructor(options: { app?: Router; router?: Router; methods?: Method[] } = {}) {\n    this._router = options.router || options.app;\n    this._methods = (Array.isArray(options.methods) ? options.methods : []).concat('use');\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(): void {\n    if (!this._router) {\n      logger.error('ExpressIntegration is missing an Express instance');\n      return;\n    }\n    instrumentMiddlewares(this._router, this._methods);\n  }\n}\n\n/**\n * Wraps original middleware function in a tracing call, which stores the info about the call as a span,\n * and finishes it once the middleware is done invoking.\n *\n * Express middlewares have 3 various forms, thus we have to take care of all of them:\n * // sync\n * app.use(function (req, res) { ... })\n * // async\n * app.use(function (req, res, next) { ... })\n * // error handler\n * app.use(function (err, req, res, next) { ... })\n *\n * They all internally delegate to the `router[method]` of the given application instance.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types, @typescript-eslint/no-explicit-any\nfunction wrap(fn: Function, method: Method): (...args: any[]) => void {\n  const arity = fn.length;\n\n  switch (arity) {\n    case 2: {\n      return function(this: NodeJS.Global, req: unknown, res: ExpressResponse & SentryTracingResponse): void {\n        const transaction = res.__sentry_transaction;\n        if (transaction) {\n          const span = transaction.startChild({\n            description: fn.name,\n            op: `middleware.${method}`,\n          });\n          res.once('finish', () => {\n            span.finish();\n          });\n        }\n        return fn.call(this, req, res);\n      };\n    }\n    case 3: {\n      return function(\n        this: NodeJS.Global,\n        req: unknown,\n        res: ExpressResponse & SentryTracingResponse,\n        next: () => void,\n      ): void {\n        const transaction = res.__sentry_transaction;\n        const span = transaction?.startChild({\n          description: fn.name,\n          op: `middleware.${method}`,\n        });\n        fn.call(this, req, res, function(this: NodeJS.Global, ...args: unknown[]): void {\n          span?.finish();\n          next.call(this, ...args);\n        });\n      };\n    }\n    case 4: {\n      return function(\n        this: NodeJS.Global,\n        err: Error,\n        req: Request,\n        res: Response & SentryTracingResponse,\n        next: () => void,\n      ): void {\n        const transaction = res.__sentry_transaction;\n        const span = transaction?.startChild({\n          description: fn.name,\n          op: `middleware.${method}`,\n        });\n        fn.call(this, err, req, res, function(this: NodeJS.Global, ...args: unknown[]): void {\n          span?.finish();\n          next.call(this, ...args);\n        });\n      };\n    }\n    default: {\n      throw new Error(`Express middleware takes 2-4 arguments. Got: ${arity}`);\n    }\n  }\n}\n\n/**\n * Takes all the function arguments passed to the original `app` or `router` method, eg. `app.use` or `router.use`\n * and wraps every function, as well as array of functions with a call to our `wrap` method.\n * We have to take care of the arrays as well as iterate over all of the arguments,\n * as `app.use` can accept middlewares in few various forms.\n *\n * app.use([<path>], <fn>)\n * app.use([<path>], <fn>, ...<fn>)\n * app.use([<path>], ...<fn>[])\n */\nfunction wrapMiddlewareArgs(args: unknown[], method: Method): unknown[] {\n  return args.map((arg: unknown) => {\n    if (typeof arg === 'function') {\n      return wrap(arg, method);\n    }\n\n    if (Array.isArray(arg)) {\n      return arg.map((a: unknown) => {\n        if (typeof a === 'function') {\n          return wrap(a, method);\n        }\n        return a;\n      });\n    }\n\n    return arg;\n  });\n}\n\n/**\n * Patches original router to utilize our tracing functionality\n */\nfunction patchMiddleware(router: Router, method: Method): Router {\n  const originalCallback = router[method];\n\n  router[method] = function(...args: unknown[]): void {\n    return originalCallback.call(this, ...wrapMiddlewareArgs(args, method));\n  };\n\n  return router;\n}\n\n/**\n * Patches original router methods\n */\nfunction instrumentMiddlewares(router: Router, methods: Method[] = []): void {\n  methods.forEach((method: Method) => patchMiddleware(router, method));\n}\n"]}