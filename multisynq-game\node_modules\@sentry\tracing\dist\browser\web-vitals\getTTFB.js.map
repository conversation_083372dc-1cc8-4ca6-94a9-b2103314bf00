{"version": 3, "file": "getTTFB.js", "sourceRoot": "", "sources": ["../../../src/browser/web-vitals/getTTFB.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAEH,uCAAgD;AAEhD,+CAA8C;AAG9C,IAAM,MAAM,GAAG,uBAAe,EAAU,CAAC;AAEzC,IAAM,SAAS,GAAG,UAAC,QAAoB;IACrC,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE;QACtC,0DAA0D;QAC1D,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;KACzB;SAAM;QACL,4DAA4D;QAC5D,gBAAgB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;KACxC;AACH,CAAC,CAAC;AAEF,IAAM,uCAAuC,GAAG;IAC9C,yEAAyE;IACzE,mDAAmD;IACnD,IAAM,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;IAEzC,IAAM,eAAe,GAAuC;QAC1D,SAAS,EAAE,YAAY;QACvB,SAAS,EAAE,CAAC;KACb,CAAC;IAEF,KAAK,IAAM,GAAG,IAAI,MAAM,EAAE;QACxB,IAAI,GAAG,KAAK,iBAAiB,IAAI,GAAG,KAAK,QAAQ,EAAE;YACjD,eAAe,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAE,MAAM,CAAC,GAA8B,CAAY,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;SACjH;KACF;IACD,OAAO,eAAgD,CAAC;AAC1D,CAAC,CAAC;AAEW,QAAA,OAAO,GAAG,UAAC,QAAuB;IAC7C,IAAM,MAAM,GAAG,uBAAU,CAAC,MAAM,CAAC,CAAC;IAElC,SAAS,CAAC;QACR,IAAI;YACF,kDAAkD;YAClD,IAAM,eAAe,GACnB,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,uCAAuC,EAAE,CAAC;YAEpG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAI,eAA+C,CAAC,aAAa,CAAC;YAE7F,MAAM,CAAC,OAAO,GAAG,CAAC,eAAe,CAAC,CAAC;YAEnC,QAAQ,CAAC,MAAM,CAAC,CAAC;SAClB;QAAC,OAAO,KAAK,EAAE;YACd,cAAc;SACf;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getGlobalObject } from '@sentry/utils';\n\nimport { initMetric } from './lib/initMetric';\nimport { NavigationTimingPolyfillEntry, ReportHandler } from './types';\n\nconst global = getGlobalObject<Window>();\n\nconst afterLoad = (callback: () => void): void => {\n  if (document.readyState === 'complete') {\n    // Queue a task so the callback runs after `loadEventEnd`.\n    setTimeout(callback, 0);\n  } else {\n    // Use `pageshow` so the callback runs after `loadEventEnd`.\n    addEventListener('pageshow', callback);\n  }\n};\n\nconst getNavigationEntryFromPerformanceTiming = (): NavigationTimingPolyfillEntry => {\n  // Really annoying that TypeScript errors when using `PerformanceTiming`.\n  // eslint-disable-next-line deprecation/deprecation\n  const timing = global.performance.timing;\n\n  const navigationEntry: { [key: string]: number | string } = {\n    entryType: 'navigation',\n    startTime: 0,\n  };\n\n  for (const key in timing) {\n    if (key !== 'navigationStart' && key !== 'toJSON') {\n      navigationEntry[key] = Math.max((timing[key as keyof PerformanceTiming] as number) - timing.navigationStart, 0);\n    }\n  }\n  return navigationEntry as NavigationTimingPolyfillEntry;\n};\n\nexport const getTTFB = (onReport: ReportHandler): void => {\n  const metric = initMetric('TTFB');\n\n  afterLoad(() => {\n    try {\n      // Use the NavigationTiming L2 entry if available.\n      const navigationEntry =\n        global.performance.getEntriesByType('navigation')[0] || getNavigationEntryFromPerformanceTiming();\n\n      metric.value = metric.delta = (navigationEntry as PerformanceNavigationTiming).responseStart;\n\n      metric.entries = [navigationEntry];\n\n      onReport(metric);\n    } catch (error) {\n      // Do nothing.\n    }\n  });\n};\n"]}