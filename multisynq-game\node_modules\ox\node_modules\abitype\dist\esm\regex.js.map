{"version": 3, "file": "regex.js", "sourceRoot": "", "sources": ["../../src/regex.ts"], "names": [], "mappings": "AAAA,qGAAqG;AACrG,iEAAiE;AACjE,MAAM,UAAU,SAAS,CAAO,KAAa,EAAE,MAAc;IAC3D,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAChC,OAAO,KAAK,EAAE,MAA0B,CAAA;AAC1C,CAAC;AAED,sDAAsD;AACtD,2BAA2B;AAC3B,MAAM,CAAC,MAAM,UAAU,GAAG,sCAAsC,CAAA;AAEhE,iFAAiF;AACjF,2BAA2B;AAC3B,MAAM,CAAC,MAAM,YAAY,GACvB,8HAA8H,CAAA;AAEhI,MAAM,CAAC,MAAM,YAAY,GAAG,cAAc,CAAA"}