{"version": 3, "file": "request.d.ts", "sourceRoot": "", "sources": ["../../src/browser/request.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAG/B,eAAO,MAAM,uBAAuB,qBAAuB,CAAC;AAE5D,0CAA0C;AAC1C,MAAM,WAAW,6BAA6B;IAC5C;;;;;OAKG;IACH,cAAc,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;IAEvC;;;;OAIG;IACH,UAAU,EAAE,OAAO,CAAC;IAEpB;;;;OAIG;IACH,QAAQ,EAAE,OAAO,CAAC;IAElB;;;;;OAKG;IACH,0BAA0B,CAAC,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC;CACnD;AAED,wCAAwC;AACxC,MAAM,WAAW,SAAS;IAExB,IAAI,EAAE,GAAG,EAAE,CAAC;IACZ,SAAS,CAAC,EAAE;QACV,MAAM,EAAE,MAAM,CAAC;QACf,GAAG,EAAE,MAAM,CAAC;QAEZ,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB,CAAC;IAIF,QAAQ,CAAC,EAAE,GAAG,CAAC;IAEf,cAAc,EAAE,MAAM,CAAC;IACvB,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB;AAED,qCAAqC;AACrC,MAAM,WAAW,OAAO;IACtB,GAAG,CAAC,EAAE;QACJ,cAAc,CAAC,EAAE;YACf,MAAM,EAAE,MAAM,CAAC;YACf,GAAG,EAAE,MAAM,CAAC;YACZ,WAAW,EAAE,MAAM,CAAC;YAEpB,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;SAC3B,CAAC;QACF,sBAAsB,CAAC,EAAE,MAAM,CAAC;QAChC,gBAAgB,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,KAAK,IAAI,CAAC;QACtD,sBAAsB,CAAC,EAAE,OAAO,CAAC;KAClC,CAAC;IACF,cAAc,EAAE,MAAM,CAAC;IACvB,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB;AAED,eAAO,MAAM,oCAAoC,EAAE,6BAIlD,CAAC;AAEF,0DAA0D;AAC1D,wBAAgB,8BAA8B,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,6BAA6B,CAAC,GAAG,IAAI,CAkDtG;AAED;;GAEG;AACH,wBAAgB,aAAa,CAC3B,WAAW,EAAE,SAAS,EACtB,gBAAgB,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,EAC1C,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAC1B,IAAI,CAgEN;AAED;;GAEG;AACH,wBAAgB,WAAW,CACzB,WAAW,EAAE,OAAO,EACpB,gBAAgB,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,EAC1C,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAC1B,IAAI,CAoDN"}