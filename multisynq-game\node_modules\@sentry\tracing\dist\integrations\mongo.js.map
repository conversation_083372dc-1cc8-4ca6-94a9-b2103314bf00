{"version": 3, "file": "mongo.js", "sourceRoot": "", "sources": ["../../src/integrations/mongo.ts"], "names": [], "mappings": ";;AAEA,uCAA6D;AAM7D,IAAM,UAAU,GAAG;IACjB,WAAW;IACX,WAAW;IACX,gBAAgB;IAChB,aAAa;IACb,eAAe;IACf,YAAY;IACZ,WAAW;IACX,UAAU;IACV,MAAM;IACN,WAAW;IACX,aAAa;IACb,wBAAwB;IACxB,SAAS;IACT,kBAAkB;IAClB,mBAAmB;IACnB,kBAAkB;IAClB,SAAS;IACT,aAAa;IACb,kBAAkB;IAClB,yBAAyB;IACzB,YAAY;IACZ,WAAW;IACX,UAAU;IACV,WAAW;IACX,SAAS;IACT,wBAAwB;IACxB,QAAQ;IACR,YAAY;IACZ,OAAO;IACP,YAAY;IACZ,WAAW;CACH,CAAC;AAEX,wGAAwG;AACxG,6EAA6E;AAC7E,2GAA2G;AAC3G,0EAA0E;AAC1E,IAAM,oBAAoB,GAEtB;IACF,sGAAsG;IACtG,+DAA+D;IAC/D,SAAS,EAAE,CAAC,YAAY,CAAC;IACzB,cAAc,EAAE,CAAC,OAAO,CAAC;IACzB,WAAW,EAAE,CAAC,aAAa,CAAC;IAC5B,aAAa,EAAE,CAAC,YAAY,CAAC;IAC7B,UAAU,EAAE,CAAC,QAAQ,CAAC;IACtB,SAAS,EAAE,CAAC,QAAQ,CAAC;IACrB,QAAQ,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC;IAC1B,SAAS,EAAE,CAAC,WAAW,CAAC;IACxB,OAAO,EAAE,CAAC,OAAO,CAAC;IAClB,gBAAgB,EAAE,CAAC,QAAQ,CAAC;IAC5B,iBAAiB,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC;IAC5C,gBAAgB,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACtC,WAAW,EAAE,CAAC,SAAS,CAAC;IACxB,UAAU,EAAE,CAAC,MAAM,CAAC;IACpB,SAAS,EAAE,CAAC,KAAK,CAAC;IAClB,SAAS,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;IAC5B,MAAM,EAAE,CAAC,SAAS,CAAC;IACnB,UAAU,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC;IAC7B,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAChC,SAAS,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;CAChC,CAAC;AAgBF,4CAA4C;AAC5C;IAcE;;OAEG;IACH,eAAmB,OAA0B;QAA1B,wBAAA,EAAA,YAA0B;QAX7C;;WAEG;QACI,SAAI,GAAW,KAAK,CAAC,EAAE,CAAC;QAS7B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;YAClD,CAAC,CAAC,OAAO,CAAC,UAAU;YACpB,CAAC,CAAG,UAAsC,CAAC;QAC7C,IAAI,CAAC,mBAAmB,GAAG,oBAAoB,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC;IACjG,CAAC;IAED;;OAEG;IACI,yBAAS,GAAhB,UAAiB,CAAqC,EAAE,aAAwB;QAC9E,IAAI,UAA2B,CAAC;QAEhC,IAAI;YACF,IAAM,aAAa,GAAG,sBAAc,CAAC,MAAM,EAAE,SAAS,CAAoC,CAAC;YAC3F,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;SACvC;QAAC,OAAO,CAAC,EAAE;YACV,cAAM,CAAC,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAC3E,OAAO;SACR;QAED,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACK,qCAAqB,GAA7B,UAA8B,UAA2B,EAAE,UAAuB,EAAE,aAAwB;QAA5G,iBAEC;QADC,UAAU,CAAC,OAAO,CAAC,UAAC,SAAoB,IAAK,OAAA,KAAI,CAAC,eAAe,CAAC,UAAU,EAAE,SAAS,EAAE,aAAa,CAAC,EAA1D,CAA0D,CAAC,CAAC;IAC3G,CAAC;IAED;;OAEG;IACK,+BAAe,GAAvB,UAAwB,UAA2B,EAAE,SAAoB,EAAE,aAAwB;QACjG,IAAI,CAAC,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC;YAAE,OAAO;QAEjD,IAAM,cAAc,GAAG,IAAI,CAAC,qCAAqC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE7E,YAAI,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,EAAE,UAAS,IAAmC;YAChF,OAAO;gBAAwB,cAAkB;qBAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;oBAAlB,yBAAkB;;;gBAC/C,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACtC,IAAM,KAAK,GAAG,aAAa,EAAE,CAAC,QAAQ,EAAE,CAAC;gBACzC,IAAM,UAAU,SAAG,KAAK,0CAAE,OAAO,EAAE,CAAC;gBAEpC,0FAA0F;gBAC1F,uDAAuD;gBACvD,IAAI,OAAO,OAAO,KAAK,UAAU,IAAI,CAAC,SAAS,KAAK,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;oBACrF,IAAM,MAAI,SAAG,UAAU,0CAAE,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;oBAC3E,OAAQ,IAAI,CAAC,IAAI,OAAT,IAAI,oBAAM,IAAI,GAAK,IAAI,EAAsB,CAAC,IAAI,CAAC,UAAC,GAAY;;wBACtE,MAAA,MAAI,0CAAE,MAAM,GAAG;wBACf,OAAO,GAAG,CAAC;oBACb,CAAC,CAAC,CAAC;iBACJ;gBAED,IAAM,IAAI,SAAG,UAAU,0CAAE,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxF,OAAO,IAAI,CAAC,IAAI,OAAT,IAAI,oBAAM,IAAI,GAAK,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAE,UAAS,GAAU,EAAE,MAAe;;wBAC/E,MAAA,IAAI,0CAAE,MAAM,GAAG;wBACf,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;oBACvB,CAAC,IAAE;YACL,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,qDAAqC,GAA7C,UACE,UAA2B,EAC3B,SAAoB,EACpB,IAAe;QAEf,IAAM,IAAI,GAA8B;YACtC,cAAc,EAAE,UAAU,CAAC,cAAc;YACzC,MAAM,EAAE,UAAU,CAAC,MAAM;YACzB,SAAS,EAAE,UAAU,CAAC,SAAS;SAChC,CAAC;QACF,IAAM,WAAW,GAAgB;YAC/B,EAAE,EAAE,IAAI;YACR,WAAW,EAAE,SAAS;YACtB,IAAI,MAAA;SACL,CAAC;QAEF,uFAAuF;QACvF,gEAAgE;QAChE,IAAM,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;QAClD,IAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC;YAC5D,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC9C,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC;QAE7B,IAAI,CAAC,SAAS,IAAI,CAAC,cAAc,EAAE;YACjC,OAAO,WAAW,CAAC;SACpB;QAED,IAAI;YACF,kFAAkF;YAClF,IAAI,SAAS,KAAK,WAAW,EAAE;gBACvB,IAAA,4BAA2C,EAA1C,WAAG,EAAE,cAAqC,CAAC;gBAClD,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,aAAa,CAAC;gBAC/E,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,aAAa,CAAC;aACzF;iBAAM;gBACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACzC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC9C;aACF;SACF;QAAC,OAAO,GAAG,EAAE;YACZ,WAAW;SACZ;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IA9HD;;OAEG;IACW,QAAE,GAAW,OAAO,CAAC;IA4HrC,YAAC;CAAA,AAhID,IAgIC;AAhIY,sBAAK", "sourcesContent": ["import { Hub } from '@sentry/hub';\nimport { EventProcessor, Integration, SpanContext } from '@sentry/types';\nimport { dynamicRequire, fill, logger } from '@sentry/utils';\n\n// This allows us to use the same array for both defaults options and the type itself.\n// (note `as const` at the end to make it a union of string literal types (i.e. \"a\" | \"b\" | ... )\n// and not just a string[])\ntype Operation = typeof OPERATIONS[number];\nconst OPERATIONS = [\n  'aggregate', // aggregate(pipeline, options, callback)\n  'bulkWrite', // bulkWrite(operations, options, callback)\n  'countDocuments', // countDocuments(query, options, callback)\n  'createIndex', // createIndex(fieldOrSpec, options, callback)\n  'createIndexes', // createIndexes(indexSpecs, options, callback)\n  'deleteMany', // deleteMany(filter, options, callback)\n  'deleteOne', // deleteOne(filter, options, callback)\n  'distinct', // distinct(key, query, options, callback)\n  'drop', // drop(options, callback)\n  'dropIndex', // dropIndex(indexName, options, callback)\n  'dropIndexes', // dropIndexes(options, callback)\n  'estimatedDocumentCount', // estimatedDocumentCount(options, callback)\n  'findOne', // findOne(query, options, callback)\n  'findOneAndDelete', // findOneAndDelete(filter, options, callback)\n  'findOneAndReplace', // findOneAndReplace(filter, replacement, options, callback)\n  'findOneAndUpdate', // findOneAndUpdate(filter, update, options, callback)\n  'indexes', // indexes(options, callback)\n  'indexExists', // indexExists(indexes, options, callback)\n  'indexInformation', // indexInformation(options, callback)\n  'initializeOrderedBulkOp', // initializeOrderedBulkOp(options, callback)\n  'insertMany', // insertMany(docs, options, callback)\n  'insertOne', // insertOne(doc, options, callback)\n  'isCapped', // isCapped(options, callback)\n  'mapReduce', // mapReduce(map, reduce, options, callback)\n  'options', // options(options, callback)\n  'parallelCollectionScan', // parallelCollectionScan(options, callback)\n  'rename', // rename(newName, options, callback)\n  'replaceOne', // replaceOne(filter, doc, options, callback)\n  'stats', // stats(options, callback)\n  'updateMany', // updateMany(filter, update, options, callback)\n  'updateOne', // updateOne(filter, update, options, callback)\n] as const;\n\n// All of the operations above take `options` and `callback` as their final parameters, but some of them\n// take additional parameters as well. For those operations, this is a map of\n// { <operation name>:  [<names of additional parameters>] }, as a way to know what to call the operation's\n// positional arguments when we add them to the span's `data` object later\nconst OPERATION_SIGNATURES: {\n  [op in Operation]?: string[];\n} = {\n  // aggregate intentionally not included because `pipeline` arguments are too complex to serialize well\n  // see https://github.com/getsentry/sentry-javascript/pull/3102\n  bulkWrite: ['operations'],\n  countDocuments: ['query'],\n  createIndex: ['fieldOrSpec'],\n  createIndexes: ['indexSpecs'],\n  deleteMany: ['filter'],\n  deleteOne: ['filter'],\n  distinct: ['key', 'query'],\n  dropIndex: ['indexName'],\n  findOne: ['query'],\n  findOneAndDelete: ['filter'],\n  findOneAndReplace: ['filter', 'replacement'],\n  findOneAndUpdate: ['filter', 'update'],\n  indexExists: ['indexes'],\n  insertMany: ['docs'],\n  insertOne: ['doc'],\n  mapReduce: ['map', 'reduce'],\n  rename: ['newName'],\n  replaceOne: ['filter', 'doc'],\n  updateMany: ['filter', 'update'],\n  updateOne: ['filter', 'update'],\n};\n\ninterface MongoCollection {\n  collectionName: string;\n  dbName: string;\n  namespace: string;\n  prototype: {\n    [operation in Operation]: (...args: unknown[]) => unknown;\n  };\n}\n\ninterface MongoOptions {\n  operations?: Operation[];\n  describeOperations?: boolean | Operation[];\n}\n\n/** Tracing integration for mongo package */\nexport class Mongo implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'Mongo';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = Mongo.id;\n\n  private _operations: Operation[];\n  private _describeOperations?: boolean | Operation[];\n\n  /**\n   * @inheritDoc\n   */\n  public constructor(options: MongoOptions = {}) {\n    this._operations = Array.isArray(options.operations)\n      ? options.operations\n      : ((OPERATIONS as unknown) as Operation[]);\n    this._describeOperations = 'describeOperations' in options ? options.describeOperations : true;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(_: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void {\n    let collection: MongoCollection;\n\n    try {\n      const mongodbModule = dynamicRequire(module, 'mongodb') as { Collection: MongoCollection };\n      collection = mongodbModule.Collection;\n    } catch (e) {\n      logger.error('Mongo Integration was unable to require `mongodb` package.');\n      return;\n    }\n\n    this._instrumentOperations(collection, this._operations, getCurrentHub);\n  }\n\n  /**\n   * Patches original collection methods\n   */\n  private _instrumentOperations(collection: MongoCollection, operations: Operation[], getCurrentHub: () => Hub): void {\n    operations.forEach((operation: Operation) => this._patchOperation(collection, operation, getCurrentHub));\n  }\n\n  /**\n   * Patches original collection to utilize our tracing functionality\n   */\n  private _patchOperation(collection: MongoCollection, operation: Operation, getCurrentHub: () => Hub): void {\n    if (!(operation in collection.prototype)) return;\n\n    const getSpanContext = this._getSpanContextFromOperationArguments.bind(this);\n\n    fill(collection.prototype, operation, function(orig: () => void | Promise<unknown>) {\n      return function(this: unknown, ...args: unknown[]) {\n        const lastArg = args[args.length - 1];\n        const scope = getCurrentHub().getScope();\n        const parentSpan = scope?.getSpan();\n\n        // Check if the operation was passed a callback. (mapReduce requires a different check, as\n        // its (non-callback) arguments can also be functions.)\n        if (typeof lastArg !== 'function' || (operation === 'mapReduce' && args.length === 2)) {\n          const span = parentSpan?.startChild(getSpanContext(this, operation, args));\n          return (orig.call(this, ...args) as Promise<unknown>).then((res: unknown) => {\n            span?.finish();\n            return res;\n          });\n        }\n\n        const span = parentSpan?.startChild(getSpanContext(this, operation, args.slice(0, -1)));\n        return orig.call(this, ...args.slice(0, -1), function(err: Error, result: unknown) {\n          span?.finish();\n          lastArg(err, result);\n        });\n      };\n    });\n  }\n\n  /**\n   * Form a SpanContext based on the user input to a given operation.\n   */\n  private _getSpanContextFromOperationArguments(\n    collection: MongoCollection,\n    operation: Operation,\n    args: unknown[],\n  ): SpanContext {\n    const data: { [key: string]: string } = {\n      collectionName: collection.collectionName,\n      dbName: collection.dbName,\n      namespace: collection.namespace,\n    };\n    const spanContext: SpanContext = {\n      op: `db`,\n      description: operation,\n      data,\n    };\n\n    // If the operation takes no arguments besides `options` and `callback`, or if argument\n    // collection is disabled for this operation, just return early.\n    const signature = OPERATION_SIGNATURES[operation];\n    const shouldDescribe = Array.isArray(this._describeOperations)\n      ? this._describeOperations.includes(operation)\n      : this._describeOperations;\n\n    if (!signature || !shouldDescribe) {\n      return spanContext;\n    }\n\n    try {\n      // Special case for `mapReduce`, as the only one accepting functions as arguments.\n      if (operation === 'mapReduce') {\n        const [map, reduce] = args as { name?: string }[];\n        data[signature[0]] = typeof map === 'string' ? map : map.name || '<anonymous>';\n        data[signature[1]] = typeof reduce === 'string' ? reduce : reduce.name || '<anonymous>';\n      } else {\n        for (let i = 0; i < signature.length; i++) {\n          data[signature[i]] = JSON.stringify(args[i]);\n        }\n      }\n    } catch (_oO) {\n      // no-empty\n    }\n\n    return spanContext;\n  }\n}\n"]}