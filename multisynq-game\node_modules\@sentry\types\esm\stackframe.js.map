{"version": 3, "file": "stackframe.js", "sourceRoot": "", "sources": ["../src/stackframe.ts"], "names": [], "mappings": "", "sourcesContent": ["/** JSDoc */\nexport interface StackFrame {\n  filename?: string;\n  function?: string;\n  module?: string;\n  platform?: string;\n  lineno?: number;\n  colno?: number;\n  abs_path?: string;\n  context_line?: string;\n  pre_context?: string[];\n  post_context?: string[];\n  in_app?: boolean;\n  vars?: { [key: string]: any };\n}\n"]}