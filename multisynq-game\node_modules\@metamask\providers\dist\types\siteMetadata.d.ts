import type { JsonRpcEngine } from '@metamask/json-rpc-engine';
import type { ConsoleLike } from './utils';
/**
 * Sends site metadata over an RPC request.
 *
 * @param engine - The JSON RPC Engine to send metadata over.
 * @param log - The logging API to use.
 */
export declare function sendSiteMetadata(engine: JsonRpcEngine, log: ConsoleLike): Promise<void>;
//# sourceMappingURL=siteMetadata.d.ts.map