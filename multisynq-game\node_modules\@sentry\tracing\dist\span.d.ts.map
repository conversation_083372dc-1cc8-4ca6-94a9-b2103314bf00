{"version": 3, "file": "span.d.ts", "sourceRoot": "", "sources": ["../src/span.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,IAAI,IAAI,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAG3F,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C;;;;;GAKG;AACH,qBAAa,YAAY;IAChB,KAAK,EAAE,IAAI,EAAE,CAAM;IAE1B,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAS;gBAEd,MAAM,GAAE,MAAa;IAIxC;;;;;OAKG;IACI,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI;CAO7B;AAED;;GAEG;AACH,qBAAa,IAAK,YAAW,aAAa;IACxC;;OAEG;IACI,OAAO,EAAE,MAAM,CAAW;IAEjC;;OAEG;IACI,MAAM,EAAE,MAAM,CAAyB;IAE9C;;OAEG;IACI,YAAY,CAAC,EAAE,MAAM,CAAC;IAE7B;;OAEG;IACI,MAAM,CAAC,EAAE,UAAU,GAAG,MAAM,CAAC;IAEpC;;OAEG;IACI,OAAO,CAAC,EAAE,OAAO,CAAC;IAEzB;;OAEG;IACI,cAAc,EAAE,MAAM,CAAqB;IAElD;;OAEG;IACI,YAAY,CAAC,EAAE,MAAM,CAAC;IAE7B;;OAEG;IACI,EAAE,CAAC,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACI,WAAW,CAAC,EAAE,MAAM,CAAC;IAE5B;;OAEG;IACI,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,CAAM;IAE/C;;OAEG;IAEI,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,CAAM;IAEzC;;OAEG;IACI,YAAY,CAAC,EAAE,YAAY,CAAC;IAEnC;;OAEG;IACI,WAAW,CAAC,EAAE,WAAW,CAAC;IAEjC;;;;;;OAMG;gBACgB,WAAW,CAAC,EAAE,WAAW;IAwC5C;;;OAGG;IACI,KAAK,CACV,WAAW,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,WAAW,EAAE,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,cAAc,CAAC,CAAC,GAC7G,IAAI;IAIP;;OAEG;IACI,UAAU,CACf,WAAW,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,WAAW,EAAE,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,cAAc,CAAC,CAAC,GAC7G,IAAI;IAkBP;;OAEG;IACI,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,GAAG,IAAI;IAKlD;;OAEG;IAEI,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,IAAI;IAK7C;;OAEG;IACI,SAAS,CAAC,KAAK,EAAE,UAAU,GAAG,IAAI;IAKzC;;OAEG;IACI,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI;IAS9C;;OAEG;IACI,SAAS,IAAI,OAAO;IAI3B;;OAEG;IACI,MAAM,CAAC,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI;IAI1C;;OAEG;IACI,aAAa,IAAI,MAAM;IAQ9B;;OAEG;IACI,eAAe,IAAI;QAExB,IAAI,CAAC,EAAE;YAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;SAAE,CAAC;QAC9B,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,EAAE,CAAC,EAAE,MAAM,CAAC;QACZ,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,OAAO,EAAE,MAAM,CAAC;QAChB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,IAAI,CAAC,EAAE;YAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;SAAE,CAAC;QACpC,QAAQ,EAAE,MAAM,CAAC;KAClB;IAaD;;OAEG;IACI,MAAM,IAAI;QAEf,IAAI,CAAC,EAAE;YAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;SAAE,CAAC;QAC9B,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,EAAE,CAAC,EAAE,MAAM,CAAC;QACZ,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,OAAO,EAAE,MAAM,CAAC;QAChB,eAAe,EAAE,MAAM,CAAC;QACxB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,IAAI,CAAC,EAAE;YAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;SAAE,CAAC;QACpC,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,QAAQ,EAAE,MAAM,CAAC;KAClB;CAcF"}