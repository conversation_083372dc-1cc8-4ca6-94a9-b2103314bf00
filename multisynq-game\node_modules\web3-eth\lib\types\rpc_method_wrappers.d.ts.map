{"version": 3, "file": "rpc_method_wrappers.d.ts", "sourceRoot": "", "sources": ["../../src/rpc_method_wrappers.ts"], "names": [], "mappings": "AAmBA,OAAO,EAEN,UAAU,EACV,UAAU,EAEV,eAAe,EACf,wBAAwB,EAExB,OAAO,EAEP,gBAAgB,EAChB,KAAK,EACL,MAAM,EACN,SAAS,EACT,OAAO,EACP,cAAc,EAKd,kBAAkB,EAClB,WAAW,EACX,eAAe,EACf,mBAAmB,EACnB,mCAAmC,EACnC,iCAAiC,EACjC,wCAAwC,EACxC,wBAAwB,EAExB,eAAe,EACf,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,WAAW,CAAC;AAiBxD,OAAO,EACN,2BAA2B,EAC3B,4BAA4B,EAC5B,qBAAqB,EACrB,sBAAsB,EACtB,qBAAqB,EACrB,MAAM,YAAY,CAAC;AAYpB;;;GAGG;AACH,eAAO,MAAM,kBAAkB,gBAAuB,WAAW,CAAC,eAAe,CAAC,oBACrB,CAAC;AAG9D;;;GAGG;AACH,eAAO,MAAM,SAAS,gBAAuB,WAAW,CAAC,eAAe,CAAC,mDACpB,CAAC;AAGtD;;;GAGG;AACH,eAAO,MAAM,WAAW,gBAAuB,WAAW,CAAC,eAAe,CAAC,oBACrB,CAAC;AAEvD;;;GAGG;AACH,eAAO,MAAM,QAAQ,gBAAuB,WAAW,CAAC,eAAe,CAAC,qBACpB,CAAC;AAErD;;;GAGG;AACH,wBAAsB,WAAW,CAAC,YAAY,SAAS,UAAU,EAChE,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,YAAY,EAAE,YAAY,qEAS1B;AAED;;;GAGG;AACH,wBAAsB,WAAW,CAAC,YAAY,SAAS,UAAU,EAChE,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,YAAY,EAAE,YAAY,qEAS1B;AAED;;;GAGG;AACH,wBAAsB,uBAAuB,CAAC,YAAY,SAAS,UAAU,EAC5E,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,YAAY,EAAE,YAAY,qEAS1B;AACD;;;GAGG;AACH,wBAAsB,cAAc,CAAC,YAAY,SAAS,UAAU,EACnE,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,YAAY,EAAE,YAAY,qEAS1B;AAED;;;GAGG;AACH,wBAAsB,UAAU,CAAC,YAAY,SAAS,UAAU,EAC/D,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,OAAO,EAAE,OAAO,EAChB,WAAW,EAAE,gBAAgB,YAA2B,EACxD,YAAY,EAAE,YAAY,qEAe1B;AAED;;;GAGG;AACH,wBAAsB,YAAY,CAAC,YAAY,SAAS,UAAU,EACjE,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,OAAO,EAAE,OAAO,EAChB,WAAW,EAAE,OAAO,EACpB,WAAW,EAAE,gBAAgB,YAA2B,EACxD,YAAY,EAAE,YAAY,kEAiB1B;AAED;;;GAGG;AACH,wBAAsB,OAAO,CAAC,YAAY,SAAS,UAAU,EAC5D,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,OAAO,EAAE,OAAO,EAChB,WAAW,EAAE,gBAAgB,YAA2B,EACxD,YAAY,EAAE,YAAY,kEAe1B;AAED;;;GAGG;AACH,wBAAsB,QAAQ,CAAC,YAAY,SAAS,UAAU,EAC7D,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,KAAK,GAAE,KAAK,GAAG,gBAAgB,aAA2B,EAC1D,QAAQ,qBAAQ,EAChB,YAAY,EAAE,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmC1B;AAED;;;GAGG;AACH,wBAAsB,wBAAwB,CAAC,YAAY,SAAS,UAAU,EAC7E,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,KAAK,GAAE,KAAK,GAAG,gBAAgB,aAA2B,EAC1D,YAAY,EAAE,YAAY,qEAwB1B;AAED;;;GAGG;AACH,wBAAsB,kBAAkB,CAAC,YAAY,SAAS,UAAU,EACvE,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,KAAK,GAAE,KAAK,GAAG,gBAAgB,aAA2B,EAC1D,YAAY,EAAE,YAAY,qEAwB1B;AAED;;;GAGG;AACH,wBAAsB,QAAQ,CAAC,YAAY,SAAS,UAAU,EAC7D,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,KAAK,GAAE,KAAK,GAAG,gBAAgB,aAA2B,EAC1D,UAAU,EAAE,OAAO,EACnB,YAAY,EAAE,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4B1B;AAED;;;GAGG;AACH,wBAAsB,cAAc,CAAC,YAAY,SAAS,UAAU,EACnE,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,eAAe,EAAE,KAAK,EACtB,YAAY,GAAE,YAA8D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAkB5E;AAED;;;GAGG;AACH,wBAAsB,sBAAsB,CAAC,YAAY,SAAS,UAAU,EAC3E,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,YAAY,EAAE,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAc1B;AAED;;;GAGG;AACH,wBAAsB,uBAAuB,CAAC,YAAY,SAAS,UAAU,EAC5E,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,KAAK,GAAE,KAAK,GAAG,gBAAgB,aAA2B,EAC1D,gBAAgB,EAAE,OAAO,EACzB,YAAY,EAAE,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eA6B1B;AAED;;;GAGG;AACH,wBAAsB,qBAAqB,CAAC,YAAY,SAAS,UAAU,EAC1E,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,eAAe,EAAE,KAAK,EACtB,YAAY,EAAE,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAiC1B;AAED;;;GAGG;AACH,wBAAsB,mBAAmB,CAAC,YAAY,SAAS,UAAU,EACxE,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,OAAO,EAAE,OAAO,EAChB,WAAW,EAAE,gBAAgB,YAA2B,EACxD,YAAY,EAAE,YAAY,qEAgB1B;AAED;;;GAGG;AACH,wBAAgB,eAAe,CAC9B,YAAY,SAAS,UAAU,EAC/B,WAAW,GAAG,UAAU,CAAC,kBAAkB,EAAE,YAAY,CAAC,EAE1D,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,cAAc,EACX,WAAW,GACX,mCAAmC,GACnC,iCAAiC,GACjC,wCAAwC,EAC3C,YAAY,EAAE,YAAY,EAC1B,OAAO,GAAE,sBAAsB,CAAC,WAAW,CAAsC,EACjF,qBAAqB,CAAC,EAAE,qBAAqB,GAC3C,cAAc,CAAC,WAAW,EAAE,qBAAqB,CAAC,YAAY,CAAC,CAAC,CAgHlE;AAED;;;GAGG;AACH,wBAAgB,qBAAqB,CACpC,YAAY,SAAS,UAAU,EAC/B,WAAW,GAAG,UAAU,CAAC,kBAAkB,EAAE,YAAY,CAAC,EAE1D,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,iBAAiB,EAAE,KAAK,EACxB,YAAY,EAAE,YAAY,EAC1B,OAAO,GAAE,4BAA4B,CAAC,WAAW,CAAsC,GACrF,cAAc,CAAC,WAAW,EAAE,2BAA2B,CAAC,YAAY,CAAC,CAAC,CAuGxE;AAED;;;GAGG;AACH,wBAAsB,IAAI,CAAC,YAAY,SAAS,UAAU,EACzD,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,OAAO,EAAE,KAAK,EACd,cAAc,EAAE,OAAO,GAAG,MAAM,EAChC,YAAY,GAAE,YAA8D;;;;;;;2DAuB5E;AAED;;;GAGG;AACH,wBAAsB,eAAe,CAAC,YAAY,SAAS,UAAU,EACpE,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,WAAW,EAAE,WAAW,EACxB,YAAY,GAAE,YAA8D,qCAyB5E;AAID;;;GAGG;AACH,wBAAsB,IAAI,CAAC,YAAY,SAAS,UAAU,EACzD,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,WAAW,EAAE,eAAe,EAC5B,WAAW,GAAE,gBAA2C,EACxD,YAAY,GAAE,YAA8D,kEAe5E;AAGD;;;GAGG;AACH,wBAAsB,WAAW,CAAC,YAAY,SAAS,UAAU,EAChE,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,WAAW,EAAE,WAAW,EACxB,WAAW,EAAE,gBAAgB,YAA2B,EACxD,YAAY,EAAE,YAAY,qEAoB1B;AAGD;;;GAGG;AACH,wBAAsB,OAAO,CAAC,YAAY,SAAS,UAAU,EAC5D,WAAW,EAAE,WAAW,CAAC,mBAAmB,CAAC,EAC7C,MAAM,EAAE,MAAM,EACd,YAAY,EAAE,YAAY;;;;;;;;;;;MAgC1B;AAED;;;GAGG;AACH,wBAAsB,UAAU,CAAC,YAAY,SAAS,UAAU,EAC/D,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,YAAY,EAAE,YAAY,qEAU1B;AAED;;;GAGG;AACH,wBAAsB,QAAQ,CAAC,YAAY,SAAS,UAAU,EAC7D,WAAW,EAAE,WAAW,CAAC,mBAAmB,CAAC,EAC7C,OAAO,EAAE,OAAO,EAChB,WAAW,EAAE,KAAK,EAAE,EACpB,WAAW,EAAE,gBAAgB,YAA2B,EACxD,YAAY,EAAE,YAAY;;;;;;;;;;;GAsB1B;AAID;;;GAGG;AACH,wBAAsB,aAAa,CAAC,YAAY,SAAS,UAAU,EAClE,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,UAAU,EAAE,OAAO,EACnB,WAAW,EAAE,gBAAgB,YAA2B,EACxD,iBAAiB,EAAE,OAAO,EAAE,EAC5B,YAAY,EAAE,YAAY;;;;;GA+B1B;AAED;;;GAGG;AACH,wBAAsB,gBAAgB,CAAC,YAAY,SAAS,UAAU,EACrE,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,WAAW,EAAE,wBAAwB,EACrC,WAAW,EAAE,gBAAgB,YAA2B,EACxD,YAAY,EAAE,YAAY;;;;;;GAmB1B;AAED;;;GAGG;AACH,wBAAsB,aAAa,CAAC,YAAY,SAAS,UAAU,EAClE,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,EACzC,OAAO,EAAE,OAAO,EAChB,SAAS,EAAE,eAAe,EAC1B,SAAS,EAAE,OAAO,EAClB,YAAY,EAAE,YAAY,mBAU1B"}