{"version": 3, "file": "transport.js", "sourceRoot": "", "sources": ["../src/transport.ts"], "names": [], "mappings": "", "sourcesContent": ["import { DsnLike } from './dsn';\nimport { Event } from './event';\nimport { Response } from './response';\nimport { Session } from './session';\n\n/** Transport used sending data to Sentry */\nexport interface Transport {\n  /**\n   * Sends the event to the Store endpoint in Sentry.\n   *\n   * @param event Event that should be sent to Sentry.\n   */\n  sendEvent(event: Event): PromiseLike<Response>;\n\n  /**\n   * Sends the session to the Store endpoint in Sentry.\n   *\n   * @param body Session that should be sent to Sentry.\n   */\n  sendSession?(session: Session): PromiseLike<Response>;\n\n  /**\n   * Call this function to wait until all pending requests have been sent.\n   *\n   * @param timeout Number time in ms to wait until the buffer is drained.\n   */\n  close(timeout?: number): PromiseLike<boolean>;\n}\n\n/** JSDoc */\nexport type TransportClass<T extends Transport> = new (options: TransportOptions) => T;\n\n/** JSDoc */\nexport interface TransportOptions {\n  /** Sentry DSN */\n  dsn: DsnLike;\n  /** Define custom headers */\n  headers?: { [key: string]: string };\n  /** Set a HTTP proxy that should be used for outbound requests. */\n  httpProxy?: string;\n  /** Set a HTTPS proxy that should be used for outbound requests. */\n  httpsProxy?: string;\n  /** HTTPS proxy certificates path */\n  caCerts?: string;\n  /** Fetch API init parameters */\n  fetchParameters?: { [key: string]: string };\n}\n"]}