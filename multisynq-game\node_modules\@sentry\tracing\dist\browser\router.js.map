{"version": 3, "file": "router.js", "sourceRoot": "", "sources": ["../../src/browser/router.ts"], "names": [], "mappings": ";AACA,uCAAmF;AAEnF,IAAM,MAAM,GAAG,uBAAe,EAAU,CAAC;AAEzC;;GAEG;AACH,SAAgB,6BAA6B,CAC3C,gBAAgE,EAChE,0BAA0C,EAC1C,gCAAgD;IADhD,2CAAA,EAAA,iCAA0C;IAC1C,iDAAA,EAAA,uCAAgD;IAEhD,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;QAC/B,cAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;QACpF,OAAO;KACR;IAED,IAAI,WAAW,GAAuB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;IAE3D,IAAI,iBAAgC,CAAC;IACrC,IAAI,0BAA0B,EAAE;QAC9B,iBAAiB,GAAG,gBAAgB,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;KAC1F;IAED,IAAI,gCAAgC,EAAE;QACpC,iCAAyB,CAAC;YACxB,QAAQ,EAAE,UAAC,EAA2C;oBAAzC,UAAE,EAAE,cAAI;gBACnB;;;;;;;;mBAQG;gBACH,IAAI,IAAI,KAAK,SAAS,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;oBACvE,WAAW,GAAG,SAAS,CAAC;oBACxB,OAAO;iBACR;gBAED,IAAI,IAAI,KAAK,EAAE,EAAE;oBACf,WAAW,GAAG,SAAS,CAAC;oBACxB,IAAI,iBAAiB,EAAE;wBACrB,cAAM,CAAC,GAAG,CAAC,sDAAoD,iBAAiB,CAAC,EAAI,CAAC,CAAC;wBACvF,gGAAgG;wBAChG,iBAAiB,CAAC,MAAM,EAAE,CAAC;qBAC5B;oBACD,iBAAiB,GAAG,gBAAgB,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;iBAC5F;YACH,CAAC;YACD,IAAI,EAAE,SAAS;SAChB,CAAC,CAAC;KACJ;AACH,CAAC;AA/CD,sEA+CC", "sourcesContent": ["import { Transaction, TransactionContext } from '@sentry/types';\nimport { addInstrumentationHandler, getGlobalObject, logger } from '@sentry/utils';\n\nconst global = getGlobalObject<Window>();\n\n/**\n * Default function implementing pageload and navigation transactions\n */\nexport function defaultRoutingInstrumentation<T extends Transaction>(\n  startTransaction: (context: TransactionContext) => T | undefined,\n  startTransactionOnPageLoad: boolean = true,\n  startTransactionOnLocationChange: boolean = true,\n): void {\n  if (!global || !global.location) {\n    logger.warn('Could not initialize routing instrumentation due to invalid location');\n    return;\n  }\n\n  let startingUrl: string | undefined = global.location.href;\n\n  let activeTransaction: T | undefined;\n  if (startTransactionOnPageLoad) {\n    activeTransaction = startTransaction({ name: global.location.pathname, op: 'pageload' });\n  }\n\n  if (startTransactionOnLocationChange) {\n    addInstrumentationHandler({\n      callback: ({ to, from }: { to: string; from?: string }) => {\n        /**\n         * This early return is there to account for some cases where a navigation transaction starts right after\n         * long-running pageload. We make sure that if `from` is undefined and a valid `startingURL` exists, we don't\n         * create an uneccessary navigation transaction.\n         *\n         * This was hard to duplicate, but this behavior stopped as soon as this fix was applied. This issue might also\n         * only be caused in certain development environments where the usage of a hot module reloader is causing\n         * errors.\n         */\n        if (from === undefined && startingUrl && startingUrl.indexOf(to) !== -1) {\n          startingUrl = undefined;\n          return;\n        }\n\n        if (from !== to) {\n          startingUrl = undefined;\n          if (activeTransaction) {\n            logger.log(`[Tracing] Finishing current transaction with op: ${activeTransaction.op}`);\n            // If there's an open transaction on the scope, we need to finish it before creating an new one.\n            activeTransaction.finish();\n          }\n          activeTransaction = startTransaction({ name: global.location.pathname, op: 'navigation' });\n        }\n      },\n      type: 'history',\n    });\n  }\n}\n"]}