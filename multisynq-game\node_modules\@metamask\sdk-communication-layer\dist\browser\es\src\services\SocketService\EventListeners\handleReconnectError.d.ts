/**
 * Returns a handler function to handle the 'reconnect_error' event.
 * This handler logs a debug message indicating a reconnection error and includes the error details.
 *
 * @param error The error object representing the reconnection error.
 * @returns {Function} A handler function for the 'reconnect_error' event.
 */
export declare function handleReconnectError(): (error: any) => void;
//# sourceMappingURL=handleReconnectError.d.ts.map