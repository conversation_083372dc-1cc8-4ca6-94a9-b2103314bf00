{"name": "@types/lru-cache", "version": "5.1.1", "description": "TypeScript definitions for lru-cache", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/lru-cache", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/Bartvds", "githubUsername": "Bartvds"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/lru-cache"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "0ca9e508a03760598fc46f2d2546f293575b69fd823ccc42a80c56f579d650db", "typeScriptVersion": "3.6"}