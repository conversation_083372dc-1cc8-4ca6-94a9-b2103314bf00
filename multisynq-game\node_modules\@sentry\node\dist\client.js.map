{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../src/client.ts"], "names": [], "mappings": ";;AAAA,qCAAiD;AAGjD,qCAAqD;AACrD,qCAAkD;AAElD;;;;;GAKG;AACH;IAAgC,sCAAoC;IAClE;;;OAGG;IACH,oBAAmB,OAAoB;eACrC,kBAAM,qBAAW,EAAE,OAAO,CAAC;IAC7B,CAAC;IAED;;OAEG;IACO,kCAAa,GAAvB,UAAwB,KAAY,EAAE,KAAa,EAAE,IAAgB;QACnE,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,IAAI,MAAM,CAAC;QAC1C,KAAK,CAAC,GAAG,yCACJ,KAAK,CAAC,GAAG,KACZ,IAAI,EAAE,kBAAQ,EACd,QAAQ,mBACH,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAC5C;oBACE,IAAI,EAAE,kBAAkB;oBACxB,OAAO,EAAE,qBAAW;iBACrB;gBAEH,OAAO,EAAE,qBAAW,GACrB,CAAC;QAEF,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE;YAChC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,UAAU,CAAC;SAClD;QAED,OAAO,iBAAM,aAAa,YAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IACH,iBAAC;AAAD,CAAC,AAjCD,CAAgC,iBAAU,GAiCzC;AAjCY,gCAAU", "sourcesContent": ["import { <PERSON><PERSON><PERSON>, Scope } from '@sentry/core';\nimport { Event, EventHint } from '@sentry/types';\n\nimport { NodeBackend, NodeOptions } from './backend';\nimport { SDK_NAME, SDK_VERSION } from './version';\n\n/**\n * The Sentry Node SDK Client.\n *\n * @see NodeOptions for documentation on configuration options.\n * @see SentryClient for usage documentation.\n */\nexport class NodeClient extends BaseClient<NodeBackend, NodeOptions> {\n  /**\n   * Creates a new Node SDK instance.\n   * @param options Configuration options for this SDK.\n   */\n  public constructor(options: NodeOptions) {\n    super(NodeBackend, options);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  protected _prepareEvent(event: Event, scope?: Scope, hint?: EventHint): PromiseLike<Event | null> {\n    event.platform = event.platform || 'node';\n    event.sdk = {\n      ...event.sdk,\n      name: SDK_NAME,\n      packages: [\n        ...((event.sdk && event.sdk.packages) || []),\n        {\n          name: 'npm:@sentry/node',\n          version: SDK_VERSION,\n        },\n      ],\n      version: SDK_VERSION,\n    };\n\n    if (this.getOptions().serverName) {\n      event.server_name = this.getOptions().serverName;\n    }\n\n    return super._prepareEvent(event, scope, hint);\n  }\n}\n"]}