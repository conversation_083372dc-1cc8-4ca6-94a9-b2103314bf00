# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v2.4.12](https://github.com/browserify/sha.js/compare/v2.4.11...v2.4.12) - 2025-07-01

### Commits

- [eslint] switch to eslint [`7acadfb`](https://github.com/browserify/sha.js/commit/7acadfbd3abb558880212b20669fcb09e1aa1c58)
- [meta] add `auto-changelog` [`b46e711`](https://github.com/browserify/sha.js/commit/b46e7116ebeaa82f34bbf2d7494fff7ef46eab3e)
- [eslint] fix package.json indentation [`df9d521`](https://github.com/browserify/sha.js/commit/df9d521e16ddf55dc877c43c05706d43c057fad4)
- [Tests] migrate from travis to GHA [`c43c64a`](https://github.com/browserify/sha.js/commit/c43c64adc6d3607d470538df72338fc02e63bc24)
- [Fix] support multi-byte wide typed arrays [`f2a258e`](https://github.com/browserify/sha.js/commit/f2a258e9f2d0fcd113bfbaa49706e1ac0d979ba5)
- [meta] reorder package.json [`d8d77c0`](https://github.com/browserify/sha.js/commit/d8d77c0a729c99593e304047f9d4335b498fd9ed)
- [meta] add `npmignore` [`35aec35`](https://github.com/browserify/sha.js/commit/35aec35c667b606b2495be3e4186bbe977b9e087)
- [Tests] avoid console logs [`73e33ae`](https://github.com/browserify/sha.js/commit/73e33ae0ca6bca232627cac7473028e1d218f67e)
- [Tests] fix tests run in batch [`2629130`](https://github.com/browserify/sha.js/commit/262913006e94616c8cd245ef6bd61bc4410b29e3)
- [Tests] drop node requirement to 0.10 [`00c7f23`](https://github.com/browserify/sha.js/commit/00c7f234aa3bdbd427ffeb929bacbb05334eb3e9)
- [Dev Deps] update `buffer`, `hash-test-vectors`, `standard`, `tape`, `typedarray` [`92b5de5`](https://github.com/browserify/sha.js/commit/92b5de5f67472d9f18413d38ad5b9aba29ff4c22)
- [Tests] drop node requirement to v3 [`9b5eca8`](https://github.com/browserify/sha.js/commit/9b5eca80fd9bb21cf05bdf43ce42661f1bbafeaa)
- [meta] set engines to `&gt;= 4` [`807084c`](https://github.com/browserify/sha.js/commit/807084c5c0f943459e89838252cafbd175b549b7)
- Only apps should have lockfiles [`c72789c`](https://github.com/browserify/sha.js/commit/c72789c7a129cf453d44008ba27a88b90ac7989b)
- [Deps] update `inherits`, `safe-buffer` [`5428cfc`](https://github.com/browserify/sha.js/commit/5428cfc6f7177ad1a41c837b9387308848db96de)
- [Dev Deps] update `@ljharb/eslint-config` [`2dbe0aa`](https://github.com/browserify/sha.js/commit/2dbe0aab419e90add5032c70c9663b8fc562adb8)
- update README to reflect LICENSE [`8938256`](https://github.com/browserify/sha.js/commit/8938256dbb2241a7c749e4a399dbaff48cbe8e95)
- [Dev Deps] add missing peer dep [`d528896`](https://github.com/browserify/sha.js/commit/d52889688ce524e63570f35e448635a29e6dd791)
- [Dev Deps] remove unused `buffer` dep [`94ca724`](https://github.com/browserify/sha.js/commit/94ca7247f467ef045f41d534708bf7c700e03828)

## [v2.4.11](https://github.com/browserify/sha.js/compare/v2.4.10...v2.4.11) - 2018-03-20

### Merged

- Project is bound by MIT AND BSD-3-Clause licenses. [`#55`](https://github.com/browserify/sha.js/pull/55)

## [v2.4.10](https://github.com/browserify/sha.js/compare/v2.4.9...v2.4.10) - 2018-01-22

### Merged

- Modified greater than uint32 bits data test [`#53`](https://github.com/browserify/sha.js/pull/53)
- convert lowBits to unsigned in hash.js [`#51`](https://github.com/browserify/sha.js/pull/51)

### Commits

- Simplify bigData allocation [`107141a`](https://github.com/browserify/sha.js/commit/107141ac2c4ca61538e4ad9622cd0c2e21d38095)
- Modified large file test [`9d037bd`](https://github.com/browserify/sha.js/commit/9d037bd51e84d0d77aa56bb94ed2af2b436d9d66)

## [v2.4.9](https://github.com/browserify/sha.js/compare/v2.4.8...v2.4.9) - 2017-09-25

### Merged

- Buffer: use alloc/allocUnsafe/from instead new [`#50`](https://github.com/browserify/sha.js/pull/50)
- Change "new shajs.SHA256()" to lowercase to make it actually work. [`#48`](https://github.com/browserify/sha.js/pull/48)
- drop Node &lt;4 [`#46`](https://github.com/browserify/sha.js/pull/46)
- hash: _update never returns anything [`#45`](https://github.com/browserify/sha.js/pull/45)
- README: remove typed array comments, re-format support algorithms [`#40`](https://github.com/browserify/sha.js/pull/40)
- Fix digesting of large data (more than MAX_UINT32 bits) [`#43`](https://github.com/browserify/sha.js/pull/43)
- use buffer module [`#41`](https://github.com/browserify/sha.js/pull/41)

### Commits

- tests: compare hex, not byte-by-byte (easier debugging) [`5d5a8d8`](https://github.com/browserify/sha.js/commit/5d5a8d882b614060b774e195821a43051f3345b7)
- hash: remove repeated remainder calculation [`158bc83`](https://github.com/browserify/sha.js/commit/158bc835fbffbbd80f93c97ba0db8e7da7db9c0e)
- tests: use safe-buffer constructors [`1ac913b`](https://github.com/browserify/sha.js/commit/1ac913b8e043d495c899a1c52258e8e4e970ee95)
- hash: increase readability of block-by-block hashing [`e9ff865`](https://github.com/browserify/sha.js/commit/e9ff865980615cb8ee2730c6868e7f6781af3c5b)
- use safe-buffer [`22adba6`](https://github.com/browserify/sha.js/commit/22adba6c745ca703cce356faa988dfe1d84eefa4)
- Add test for large data [`e963695`](https://github.com/browserify/sha.js/commit/e9636950b88c8e2a0b012c19f4957229d409b04f)
- tests: formatting [`678c338`](https://github.com/browserify/sha.js/commit/678c3380273516094e32eb79c50e4bce18da6346)
- Fix digesting of large data [`aee24f1`](https://github.com/browserify/sha.js/commit/aee24f1e0d7fefca68633e6c1be52670fb65a1a5)
- hash: update never returns anything [`d308cb0`](https://github.com/browserify/sha.js/commit/d308cb0004a0f3e0fcb7a27ea52868384c654c95)
- hash: rm unnecessary _s state [`388d45e`](https://github.com/browserify/sha.js/commit/388d45ec3a040e7f6ffeecd077498baf32e270e9)
- npmignore: ignore test/ [`03702a8`](https://github.com/browserify/sha.js/commit/03702a8032fe2bc0b033860bec37c09c1d4af44b)
- package: bump standard [`8551e53`](https://github.com/browserify/sha.js/commit/8551e53f389cbe3728b21cd62e2df917b4dad9d6)

## [v2.4.8](https://github.com/browserify/sha.js/compare/v2.4.7...v2.4.8) - 2016-11-11

### Commits

- travis: add 6 [`62a582c`](https://github.com/browserify/sha.js/commit/62a582ccebffa04f7b281c680095ffd7a7107e12)

## [v2.4.7](https://github.com/browserify/sha.js/compare/v2.4.6...v2.4.7) - 2016-11-10

### Commits

- re-add bin.js [`30546ca`](https://github.com/browserify/sha.js/commit/30546ca68e683e7fcb4d6c372e48c6b9fda35b4c)

## [v2.4.6](https://github.com/browserify/sha.js/compare/v2.4.5...v2.4.6) - 2016-11-10

### Merged

- use hash-base [`#36`](https://github.com/browserify/sha.js/pull/36)
- travis: add node 6 [`#38`](https://github.com/browserify/sha.js/pull/38)
- 2.4.5 [`#35`](https://github.com/browserify/sha.js/pull/35)

### Commits

- update implementations [`aba27f9`](https://github.com/browserify/sha.js/commit/aba27f9132de39dca4089a120b6c66e097fcd865)
- update tests [`8522be9`](https://github.com/browserify/sha.js/commit/8522be9bc5abc34adf3cf4b17c132471c0f4f80a)
- remove bin.js [`f7c86a7`](https://github.com/browserify/sha.js/commit/f7c86a70d6a70dd807cce06f811d3cbf9cbebff0)
- update README.md [`8eec0fb`](https://github.com/browserify/sha.js/commit/8eec0fbf2025cdf9c5d2f8877d3fe3e276dbda88)
- move shaX to lib directory [`cf2ab1d`](https://github.com/browserify/sha.js/commit/cf2ab1dc9bdd434dfd3afd043f0956894c931ad2)
- travis: add 6 [`891c962`](https://github.com/browserify/sha.js/commit/891c96228dd4cb9777fbae169e8ee8f2c3dc022c)

## [v2.4.5](https://github.com/browserify/sha.js/compare/v2.4.4...v2.4.5) - 2016-02-26

### Merged

- Improve performace [`#34`](https://github.com/browserify/sha.js/pull/34)
- Add node v4 and v5 to travis config [`#33`](https://github.com/browserify/sha.js/pull/33)

### Commits

- Update package.json [`2b250d6`](https://github.com/browserify/sha.js/commit/2b250d6358efed8c9476805ccb86e20c63e721a6)

## [v2.4.4](https://github.com/browserify/sha.js/compare/v2.4.3...v2.4.4) - 2015-09-19

### Merged

- inline Sigma functions [`#32`](https://github.com/browserify/sha.js/pull/32)

## [v2.4.3](https://github.com/browserify/sha.js/compare/v2.4.2...v2.4.3) - 2015-09-15

### Merged

- Remove testling [`#31`](https://github.com/browserify/sha.js/pull/31)

### Fixed

- Adds npm badge (resolves #28) [`#28`](https://github.com/browserify/sha.js/issues/28)

### Commits

- fix standard issues [`52659f7`](https://github.com/browserify/sha.js/commit/52659f73bdc9ce1147da010cb303f3f008428498)
- README: update badge paths [`66a0b4c`](https://github.com/browserify/sha.js/commit/66a0b4c50b3499e2db37c4d1b0545d6536bc2f3b)
- Update README.md [`ca03356`](https://github.com/browserify/sha.js/commit/ca03356cbf74ea5b57df4d4f5ccc1e5557a75966)

## [v2.4.2](https://github.com/browserify/sha.js/compare/v2.4.1...v2.4.2) - 2015-06-05

### Merged

- Use standard [`#26`](https://github.com/browserify/sha.js/pull/26)

### Commits

- sha*: adhere to standard [`74f5fc4`](https://github.com/browserify/sha.js/commit/74f5fc4741447385f5691cb3140cf716a1288312)
- tests: adhere to standard [`e6851ca`](https://github.com/browserify/sha.js/commit/e6851ca9bb0843fa90d8eee7d3f7b3f9d1bbe4fb)
- bin: adhere to standard [`d1a23ab`](https://github.com/browserify/sha.js/commit/d1a23ab987eed4a6b940161693a805fa67c6e16d)
- vectors: adhere to standard [`5657c76`](https://github.com/browserify/sha.js/commit/5657c76f23e92d268010e0fd5880d5c52014eab2)
- hexpp: adhere to stnadard [`2aa2707`](https://github.com/browserify/sha.js/commit/2aa27074799df136b6b49cd0cdb272fe39c742a9)
- tests: remove unused generateCount function [`4a0b095`](https://github.com/browserify/sha.js/commit/4a0b0958e287070efe0bc3d9addff9401e4bbf18)
- adds standard [`0041dbb`](https://github.com/browserify/sha.js/commit/0041dbbd440c0f2e279c6a965485d1d841db3e9e)
- index: adhere to standard [`1839fb7`](https://github.com/browserify/sha.js/commit/1839fb715518fb18077e2b02449ad3627efc3ecb)
- hash: adhere to standard [`1334d89`](https://github.com/browserify/sha.js/commit/1334d89fe96a5854c2871c6352bc6d1a7f843145)
- package: use standard 4.0.0 [`ace4747`](https://github.com/browserify/sha.js/commit/ace474780c743368934c63df64cf12f556bb86a6)
- example is sha256 not sha1 [`8eb102b`](https://github.com/browserify/sha.js/commit/8eb102b6c3faa4a87c134644d019e8c8806a5801)

## [v2.4.1](https://github.com/browserify/sha.js/compare/v2.4.0...v2.4.1) - 2015-05-19

### Merged

- Update README.md [`#22`](https://github.com/browserify/sha.js/pull/22)

## [v2.4.0](https://github.com/browserify/sha.js/compare/v2.3.6...v2.4.0) - 2015-04-05

### Commits

- sha0: add implementation [`ca6950d`](https://github.com/browserify/sha.js/commit/ca6950d53c064aa5d767e7166ea29dd0cb61a1b6)
- document legacyness of sha1 and sha0 [`4563da6`](https://github.com/browserify/sha.js/commit/4563da67ee0e86e4eea55b844c6ec73f677e094a)
- README: not just SHA1 anymore [`2a67456`](https://github.com/browserify/sha.js/commit/2a67456d5ab2c6197f8314a83ddb4a9dc59ebfd1)

## [v2.3.6](https://github.com/browserify/sha.js/compare/v2.3.5...v2.3.6) - 2015-01-14

### Commits

- transfer to crypto-browserify org [`40f1aa9`](https://github.com/browserify/sha.js/commit/40f1aa960c0e7ddc4dc933013d63df41e6362737)

## [v2.3.5](https://github.com/browserify/sha.js/compare/v2.3.4...v2.3.5) - 2015-01-14

### Commits

- sha512: same branch extraction as #18 [`f985426`](https://github.com/browserify/sha.js/commit/f9854264d841f7138f65eec36010f54043868fdd)
- sha256: extract branches out [`e5486fd`](https://github.com/browserify/sha.js/commit/e5486fde95542a1f79c568f010810b8c7b9889c4)

## [v2.3.4](https://github.com/browserify/sha.js/compare/v2.3.3...v2.3.4) - 2015-01-13

### Commits

- sha1: use a closure over separate loops [`26a75ec`](https://github.com/browserify/sha.js/commit/26a75eca5f841850a05384d8a3a95e22ee8d9617)

## [v2.3.3](https://github.com/browserify/sha.js/compare/v2.3.2...v2.3.3) - 2015-01-13

### Commits

- sha1: unroll conditionals [`f830142`](https://github.com/browserify/sha.js/commit/f8301422051bd82cdb490881904b4bd418d5049d)
- sha1: use a closure over seperate loops [`bf46619`](https://github.com/browserify/sha.js/commit/bf46619c437f14c8aec95049fc054a13ee42c779)
- sha1: inline _ft, _kt functions [`3b32ff2`](https://github.com/browserify/sha.js/commit/3b32ff2b18642d57c069152d0dba2dfdc8f6c3ac)

## [v2.3.2](https://github.com/browserify/sha.js/compare/v2.3.1...v2.3.2) - 2015-01-12

### Commits

- improve sha* code structuring consistency [`d35623d`](https://github.com/browserify/sha.js/commit/d35623d4eddad7bc6f74c6c4f20e59cfc582215d)
- sha*: avoid unnecessary var declaration separation [`d985016`](https://github.com/browserify/sha.js/commit/d9850165dd29f662ff5c9490f9922a2bc65fec73)
- sha1: format sha1_kt similar to sha1_ft for clarity [`c18e7eb`](https://github.com/browserify/sha.js/commit/c18e7eb5ea14c7f5bb99f2664b04e132feeff296)
- adds .gitignore for node_modules [`9dc2814`](https://github.com/browserify/sha.js/commit/9dc2814271d9f119f8b30367d823e72b4f98e1ed)

## [v2.3.1](https://github.com/browserify/sha.js/compare/v2.3.0...v2.3.1) - 2015-01-12

### Commits

- Use inherits module instead of util [`aef9b82`](https://github.com/browserify/sha.js/commit/aef9b82c629f6ebaf346dab15c2a9bd30fb05aa6)

## [v2.3.0](https://github.com/browserify/sha.js/compare/v2.2.7...v2.3.0) - 2014-11-18

### Commits

- clean up factories [`996be1c`](https://github.com/browserify/sha.js/commit/996be1cb6a62a479dfb4758c5c431c6381c226cd)
- sha224 and 384 [`56694e5`](https://github.com/browserify/sha.js/commit/56694e5db70844f11a6a4082a549339d3b24ea95)
- add prepublish safety script [`84bde3c`](https://github.com/browserify/sha.js/commit/84bde3cb011f2370034c135727d602522d34e078)

## [v2.2.7](https://github.com/browserify/sha.js/compare/v2.2.6...v2.2.7) - 2014-11-06

### Commits

- use hash-test-vectors module [`526e246`](https://github.com/browserify/sha.js/commit/526e246cd58f108b410eeae3982756acd06c659c)

## [v2.2.6](https://github.com/browserify/sha.js/compare/v2.2.5...v2.2.6) - 2014-09-18

### Commits

- don't use global module [`8734884`](https://github.com/browserify/sha.js/commit/87348845d238ba6d8609f22a0972a438d4fc6ab1)
- safely check for IntArray32 existance [`e2376fd`](https://github.com/browserify/sha.js/commit/e2376fd5824fa89ad571e0ea666367511d3a02b5)

## [v2.2.5](https://github.com/browserify/sha.js/compare/v2.2.4...v2.2.5) - 2014-09-16

### Commits

- move buffer and typedarray into devdeps [`68797f9`](https://github.com/browserify/sha.js/commit/68797f971f55bcf53014e13d9b83adcc0e113ea0)

## [v2.2.4](https://github.com/browserify/sha.js/compare/v2.2.3...v2.2.4) - 2014-09-16

### Commits

- merge [`7d8b28f`](https://github.com/browserify/sha.js/commit/7d8b28f7627c82ea289e9396d1b93139264e4e1f)
- Fall back to normal array if no typed arrays [`8ca8dfc`](https://github.com/browserify/sha.js/commit/8ca8dfc025e5b2de4a126235b2f3eb4a1046b2d6)
- Don't use console.error [`6e0bd2d`](https://github.com/browserify/sha.js/commit/6e0bd2d8f3db4c267fbaebcbd1b542bc03b1e356)

## [v2.2.3](https://github.com/browserify/sha.js/compare/v2.2.2...v2.2.3) - 2014-09-16

### Commits

- fix test [`b4e83fa`](https://github.com/browserify/sha.js/commit/b4e83fa8ef732e90c399fcde5f55f8417d623524)

## [v2.2.2](https://github.com/browserify/sha.js/compare/v2.2.1...v2.2.2) - 2014-09-16

### Merged

- Copyright to contributors [`#10`](https://github.com/browserify/sha.js/pull/10)

### Commits

- LICENSE: update to include all contributors [`ac05b4d`](https://github.com/browserify/sha.js/commit/ac05b4d8bfca0c67edd8f20808d61b1aea980ebd)

## [v2.2.1](https://github.com/browserify/sha.js/compare/v2.2.0...v2.2.1) - 2014-09-16

### Commits

- document implemented hashes [`d123901`](https://github.com/browserify/sha.js/commit/d123901fe28148dce55637ed7942cd4953c9f448)

## [v2.2.0](https://github.com/browserify/sha.js/compare/v2.1.8...v2.2.0) - 2014-09-16

### Commits

- sha512: add implementation [`3e19416`](https://github.com/browserify/sha.js/commit/3e1941651b20741579c4adfcf69aa0bd607ef932)
- fixtures: remove unused md4 data [`13e43c5`](https://github.com/browserify/sha.js/commit/13e43c59a7109d31147f45a6619af6f8bfa11923)
- get tests working correctly [`01e393f`](https://github.com/browserify/sha.js/commit/01e393fbc4253ce82cf1f57f10c76b087a4b7787)
- remove utils.js [`418d59d`](https://github.com/browserify/sha.js/commit/418d59d40315a50972244ab4103c6d2a59dd86a2)
- fixtures: cleanup of vectors generation [`40f50cc`](https://github.com/browserify/sha.js/commit/40f50ccc29db3f689a04ebfecac2753c895398be)
- sha: jshint cleanup [`a04fae0`](https://github.com/browserify/sha.js/commit/a04fae03acdfb3bbfc7fbf15d928244364b5083a)
- hash: adhere to NIST paper properly [`fb2e39f`](https://github.com/browserify/sha.js/commit/fb2e39f86ce80948b697ac7d0d5f9b7f99c0672c)
- hash: increase verbosity [`b431a1a`](https://github.com/browserify/sha.js/commit/b431a1a24d5d37f856592aabe4593196d60b3b7f)
- hash: use update() argument instead [`0703b9d`](https://github.com/browserify/sha.js/commit/0703b9d38e816d71794a6604060a30e26072b6b7)
- sha: remove unused POOL [`0299989`](https://github.com/browserify/sha.js/commit/02999896280b859d5526820716c737ecbc46d0f6)
- README: add newline before testling badge [`a184d68`](https://github.com/browserify/sha.js/commit/a184d680dae744e7a6adfb73839c3feb6bd5f840)
- LICENSE: update to include all contributors [`edf48c3`](https://github.com/browserify/sha.js/commit/edf48c3b12638cafd509d51b84c39d63f6f00d3b)
- index: remove unused export [`b4de630`](https://github.com/browserify/sha.js/commit/b4de630c9e2092072d0baa5a0a5e93d00ce43c44)

## [v2.1.8](https://github.com/browserify/sha.js/compare/v2.1.7...v2.1.8) - 2014-08-31

### Merged

- check if DataView exist before using instanceof check [`#6`](https://github.com/browserify/sha.js/pull/6)

## [v2.1.7](https://github.com/browserify/sha.js/compare/v2.1.6...v2.1.7) - 2014-07-24

### Commits

- check for streaming updates [`4fc22d2`](https://github.com/browserify/sha.js/commit/4fc22d239c87d62155292ed7ccef2e36819bd7a6)
- also test with 3 partial updates [`37981e0`](https://github.com/browserify/sha.js/commit/37981e0b751e4cbb0631472aa19f5facb220cc31)
- Fix streaming updates (limit writing so it doesn't go over block size) [`50b8ddb`](https://github.com/browserify/sha.js/commit/50b8ddb4a5ec8fdaef7c51a01540b237e14a9b5e)

## [v2.1.6](https://github.com/browserify/sha.js/compare/v2.1.5...v2.1.6) - 2014-07-19

### Merged

- Fixes disparity between 'SHA1' working on node but failing in browser [`#3`](https://github.com/browserify/sha.js/pull/3)

## [v2.1.5](https://github.com/browserify/sha.js/compare/v2.1.4...v2.1.5) - 2014-06-07

### Commits

- use buffer/ [`23ee33f`](https://github.com/browserify/sha.js/commit/23ee33f8d9ebd5226f6f2fcc6dfcbfddda18af17)

## v2.1.4 - 2014-06-07

### Commits

- add tests from NIST [`422aa1f`](https://github.com/browserify/sha.js/commit/422aa1fccbd4efc5d2a72fbe7404971c45348c16)
- code to prepare nist-vectors.json [`e799a6f`](https://github.com/browserify/sha.js/commit/e799a6f8b15a9a3796dce295d9485defc7246568)
- inject Buffer dep, so can test with different implementations [`3d89958`](https://github.com/browserify/sha.js/commit/****************************************)
- initial [`c1cabff`](https://github.com/browserify/sha.js/commit/c1cabff65dc811bd9c7e7530aab90db3b1080f04)
- expose createHash, like node's crypto [`41a1c53`](https://github.com/browserify/sha.js/commit/41a1c531c7947f1bc16ff33ba8dc9335a1a932fd)
- update stuff, still one problem with finalizing some lengths... [`d91aabb`](https://github.com/browserify/sha.js/commit/d91aabb27b0320708aa19943626029510aab3cbb)
- inject Buffer dep into hash [`21df559`](https://github.com/browserify/sha.js/commit/21df55938c274a75d5d04dfd7a4c9abe41d0ce7c)
- refactor tests [`fa6f893`](https://github.com/browserify/sha.js/commit/fa6f893ea0459caa71cd603f244e35f11a617b3b)
- this is quite a bit faster [`84379b3`](https://github.com/browserify/sha.js/commit/84379b3651daca535cbc9aba987563c26d84816f)
- implement sha256! [`70a6101`](https://github.com/browserify/sha.js/commit/70a6101ba6c6a4ae2bf6b89a9feae41c2bcd9559)
- tidy [`dce6d28`](https://github.com/browserify/sha.js/commit/dce6d28d15672d8c95cfc855b8071398b8aaca67)
- move of string stuff, use dataview [`55c7003`](https://github.com/browserify/sha.js/commit/55c7003b99880e54bbd709138028f66c94f51c64)
- update to buffer incrementally [`8cbcade`](https://github.com/browserify/sha.js/commit/8cbcade0875305d3005b97b94b7cdaa61cb93f34)
- refactor, to use buffers in tests [`8e7119b`](https://github.com/browserify/sha.js/commit/8e7119b5c079f51e5c24bfa2d5c3e25536e8b677)
- this is a little faster, but not much... [`55dfc90`](https://github.com/browserify/sha.js/commit/55dfc909269e7b21fb748a5bb594ba3b92a3e03e)
- refactor util functions out [`283f192`](https://github.com/browserify/sha.js/commit/283f1923bdf0bfe48167f240062071cdd2340f78)
- more encodings [`e5071ca`](https://github.com/browserify/sha.js/commit/e5071ca79c80b4d10a2fac860026cdaa291aee27)
- more tests [`655a7be`](https://github.com/browserify/sha.js/commit/655a7be9914298ad63ea77ba4c85dc34ed5f7f9b)
- deal with endianness [`1331b1f`](https://github.com/browserify/sha.js/commit/1331b1f4a1a449c0314cdaec9b0c363a8d8357dd)
- remove custom encoding stuff - just use buffer [`b464d5b`](https://github.com/browserify/sha.js/commit/b464d5bf5cd34de2cb294108179e6967bf3aef28)
- add more encodings to write [`19ce345`](https://github.com/browserify/sha.js/commit/19ce345a06206dda644f21c602aa326597998822)
- separate basic stuff into Hash function [`fe59f0c`](https://github.com/browserify/sha.js/commit/fe59f0cb949fa9e4b5f162f54ebe7b3312b1fc0f)
- experiment using node buffers [`27f6767`](https://github.com/browserify/sha.js/commit/27f676750b9e6e042d2d2ea11ef2c4196f3b417c)
- Several Memory Related Performance Improvements [`9b9badc`](https://github.com/browserify/sha.js/commit/9b9badccae5585d0a1f563ce171635404f97108d)
- tidy [`51c40fa`](https://github.com/browserify/sha.js/commit/51c40fa0c632c5114b574199063f8366621eaaa8)
- use toggle to compare with forge, but inlining makes this the same perf, although removing safe_add improved perf a lot [`15f80b9`](https://github.com/browserify/sha.js/commit/15f80b9e7d1677962f4f0f930e13e0d90de2ef13)
- remove unused utils [`a331a15`](https://github.com/browserify/sha.js/commit/a331a1513efa3bc2cdebc955fa3832ff6306d34f)
- tests for Hash [`417c298`](https://github.com/browserify/sha.js/commit/417c29858090b53b74d91f91b8f8e830dc0384a8)
- for some reason, this is MUCH faster! [`91649a6`](https://github.com/browserify/sha.js/commit/91649a61d6fe8ab2b1f785c1833efa89feeff3b0)
- leaking globals [`7e94cf7`](https://github.com/browserify/sha.js/commit/7e94cf7758ebf70f4c876518495445e971e2eff0)
- delete fakebuffer.js [`e42d66c`](https://github.com/browserify/sha.js/commit/e42d66cf5190a8a0c45e19231ddcd2ef983f1380)
- use bigendian [`f633b94`](https://github.com/browserify/sha.js/commit/f633b94aef9504607b14a2c2805b1e491f3e61b8)
- fix digest [`fdee30b`](https://github.com/browserify/sha.js/commit/fdee30be69cc3fa58a25ae3e32d629251e0a005d)
- tidy [`6f03926`](https://github.com/browserify/sha.js/commit/6f0392697e73f03c8900ccebd4a7d706c77bdea9)
- test incremental update [`d11e6f6`](https://github.com/browserify/sha.js/commit/d11e6f69f4be8d2d1c89f8492d4de2cf2b42027b)
- fake buffer, based on DataView [`71a31b6`](https://github.com/browserify/sha.js/commit/71a31b642d3212076dbd9e54cf6b6072b03c61ad)
- command to hash a large file [`618f16d`](https://github.com/browserify/sha.js/commit/618f16de80ee4b499ab6a493e88845f161dbb0dc)
- { on end of line [`8c1a1a7`](https://github.com/browserify/sha.js/commit/8c1a1a743e740df2e04fa4a9d97a9aea5831529d)
- hammer in a piton, incase I fall off this cliff [`0a211b2`](https://github.com/browserify/sha.js/commit/0a211b2b0abda6252f59f6f3892df1411670c72f)
- basic tests for encoding [`dece220`](https://github.com/browserify/sha.js/commit/dece220424b1d2776a88b35f28d46001e13b10d7)
- tests for hex encoding [`f860f65`](https://github.com/browserify/sha.js/commit/f860f65c173ff92b07643ae55a76516de0b1dded)
- fix fakebuffer [`c421953`](https://github.com/browserify/sha.js/commit/c421953c135fbb1fa3b46df0c3710bc39e50ac4d)
- remove encoding utils [`b0a9d4b`](https://github.com/browserify/sha.js/commit/b0a9d4bc153bd8ffd23d85e5c6a51ac8d7f81d51)
- tidy [`72b825b`](https://github.com/browserify/sha.js/commit/72b825b5a071a37de7bf00f2701bc0af66618ec2)
- tests for fakebuffer [`391fc9f`](https://github.com/browserify/sha.js/commit/391fc9f84988ebc32e8e14687568d8a2418fa34f)
- avoid unnecessary overwrite, 5% improvement [`d061547`](https://github.com/browserify/sha.js/commit/d0615475d9d23ab97814f55fab2e7c6db7adb9bb)
- use dataview [`04b9dee`](https://github.com/browserify/sha.js/commit/04b9deefaf8b1646bc9191f6d955e50c36441da0)
- update vector test to cover sha256 [`aa0d4fa`](https://github.com/browserify/sha.js/commit/aa0d4faef9f05aedf388bf9ca0e5012c4fc14326)
- readme [`6a9992a`](https://github.com/browserify/sha.js/commit/6a9992a77747286d059f0335d5358a013cd0096b)
- toHex supports strings and buffers [`9e17355`](https://github.com/browserify/sha.js/commit/9e173551bba96b7a6d395311948a4887e1461e5e)
- remove redundant tests [`9c701f4`](https://github.com/browserify/sha.js/commit/9c701f4b390b25fd154f85f6b8a5b187542bc463)
- testling [`3515f2f`](https://github.com/browserify/sha.js/commit/3515f2f8c958e3b598a1b6be87ba33cd70ae1591)
- support hex encoding [`b1488b5`](https://github.com/browserify/sha.js/commit/b1488b5dd416c4525e6bc817d7b87da27b275c94)
- remove logging [`ce7d53a`](https://github.com/browserify/sha.js/commit/ce7d53af357062def163f895aa5386e3f6b7e605)
- the working buffer can use system default endianness [`3da2747`](https://github.com/browserify/sha.js/commit/3da27472f74929d223c9ca47c59c219748989981)
- use dataview [`bdba2ec`](https://github.com/browserify/sha.js/commit/bdba2ecbd5877d4e2db012eaaa489467a08fb143)
- support binary encoding [`7b0cae7`](https://github.com/browserify/sha.js/commit/7b0cae71407ec363dc0150c1676fcd0a96a7ea48)
- refactor tests, for createHash [`f424197`](https://github.com/browserify/sha.js/commit/f4241979e140b8317abeb84773136e92d8811ca6)
- Int32 is a little faster than Uint32 [`c61542e`](https://github.com/browserify/sha.js/commit/c61542e06a4c06245aca3860aa36d97e250f08b9)
- simplify bit manipulations [`7e2fc4c`](https://github.com/browserify/sha.js/commit/7e2fc4c06350b35c83d7336c4c36b0acef274373)
- tidy [`e34e8b5`](https://github.com/browserify/sha.js/commit/e34e8b540202f4f548a70d44600287f9e2bd6e82)
- load browserify.js to force native-buffer-browserify [`fd5e58a`](https://github.com/browserify/sha.js/commit/fd5e58a4caa4223fe8f69614d137f219cc11d640)
- tidyup [`12e401b`](https://github.com/browserify/sha.js/commit/12e401b47e7f62ac47edea70efa2db4a044df015)
- this tiny change make it 11% faster on 174mb file! [`f58c321`](https://github.com/browserify/sha.js/commit/f58c3212e40b9c65dc81dcf4765052d4181cf96d)
- support multiple encodings [`36506c6`](https://github.com/browserify/sha.js/commit/36506c6ded807f9a23014b4eb2385ec8ae5d681b)
- tidy [`2c664aa`](https://github.com/browserify/sha.js/commit/2c664aadf9008111c8f19d30f0b8125d0133b79c)
- update hash tests - for some reason, t.deepEqual doesn't work well on buffers? [`8e8e854`](https://github.com/browserify/sha.js/commit/8e8e8547612af4cb628400be0933edeb856f28a5)
- rename to Sha1 [`6620d1a`](https://github.com/browserify/sha.js/commit/6620d1a9a8598e7cf2799e72a786f5bafbaef7a2)
- tidy [`2313658`](https://github.com/browserify/sha.js/commit/2313658f0330b3b851ba55ada7f9ab8a11734802)
- use bops for encoding/decoding [`48d1eb9`](https://github.com/browserify/sha.js/commit/48d1eb9eeee33c2bf55aaae4112ffdf236e20fa2)
- handle large updates all at once, to pass NIST tests [`f2adc77`](https://github.com/browserify/sha.js/commit/f2adc77e49c74d34d8d6715f8b172f3d898bbe32)
- use bops [`5167411`](https://github.com/browserify/sha.js/commit/51674113e9dc35a54d1ec564c51374ad869252b9)
- use fakebuffer instead of buffer [`a6398fe`](https://github.com/browserify/sha.js/commit/a6398fe0582e88aa837a6058494ff7a9c783bd23)
- remove final, and force to Uint8Array [`c42eb76`](https://github.com/browserify/sha.js/commit/c42eb76a6afb318202fd63aff4e6de2145fe4d51)
- todo [`52ef73e`](https://github.com/browserify/sha.js/commit/52ef73e7db22937a2f47ad69e4c4e75fb03215a1)
- remove debugging stuff [`afeb954`](https://github.com/browserify/sha.js/commit/afeb95445f1aff601dba5436ef6024c818d5ed06)
- use bops@0.1.1 [`ccb7eaf`](https://github.com/browserify/sha.js/commit/ccb7eaf1f6e65d590fb4d56797f342e1139a643b)
- convert to string [`abe5373`](https://github.com/browserify/sha.js/commit/abe5373aaf0742cf10dc82b879c0848e22a611fe)
- work around tape/ff [`b95d57c`](https://github.com/browserify/sha.js/commit/b95d57c596a455b3a7b9ad8de95e4374a7d84cf8)
- remove bops [`4d9fb4d`](https://github.com/browserify/sha.js/commit/4d9fb4d8fd8332d69b055e9d1dd1ba5aad5ebb8c)
- this made no difference [`0a0ee38`](https://github.com/browserify/sha.js/commit/0a0ee38c5fead881b3bdb96c9b47a59b62451b13)
- drop support for legacy versions [`e7c530f`](https://github.com/browserify/sha.js/commit/e7c530f19a33aac5768e50dffc8fc93df4070af3)
- a few more test cases [`48ce51b`](https://github.com/browserify/sha.js/commit/48ce51b50b62bb53bd6effdfe42be232f31ffa52)
- use buffer methods [`6a572d2`](https://github.com/browserify/sha.js/commit/6a572d2a27e20c990f8a32d7c39606a0571f9fe7)
- getter for buffer length [`56c1e35`](https://github.com/browserify/sha.js/commit/56c1e35583aa53b1aaaff68d43a4bf4a0c206eea)
- more debuging [`f1c9d10`](https://github.com/browserify/sha.js/commit/f1c9d104d188fbc5ff447a8dc7976400646d3ffb)
- HAHA IT WORKS [`ee95185`](https://github.com/browserify/sha.js/commit/ee9518599957a64d46d8d45aeaae649ec85641c9)
- test coverage for binary encoding [`96e417c`](https://github.com/browserify/sha.js/commit/96e417cd12df156f6f0d3608bdd62ef9d661c41d)
- set debug mode to show time elapsed [`36d4639`](https://github.com/browserify/sha.js/commit/36d46393bde62248e5949b1954b61a96e7220c1c)
- interpret utf-8 as utf8 [`53bd808`](https://github.com/browserify/sha.js/commit/53bd8080ed929d408856b762a43076b1cad19584)
- use browserify edge case to get browser version of core module in node [`657c0a9`](https://github.com/browserify/sha.js/commit/657c0a94c914dee1a473b21f1f3c736a5e715a45)
- native-buffer-browserify -&gt; buffer [`c6a2777`](https://github.com/browserify/sha.js/commit/c6a2777e3ec85de16215f79171fbf2b561556b17)
- do not run test/test.js in the browser, it depends on node.js [`d1d4ac8`](https://github.com/browserify/sha.js/commit/d1d4ac8d27ef65c3fb1dac6b7ef612fabceeea26)
- compute correct length for binary string [`c616d74`](https://github.com/browserify/sha.js/commit/c616d7435493c67d399954d8b5673691b5405e83)
- tidy [`d176073`](https://github.com/browserify/sha.js/commit/d176073dbba34810b86e656dac3a268b438abafe)
- this is twice as fast! turns out creating DataViews is quite slow! /cc @feross [`3ba9a1f`](https://github.com/browserify/sha.js/commit/****************************************)
- use _blockLength property [`fdf1030`](https://github.com/browserify/sha.js/commit/fdf10309e4334347e8f668d6ec6d25ea16856f8e)
- allow subclass to give hash by _hash method [`d47673b`](https://github.com/browserify/sha.js/commit/d47673bc4b2b66f46f0eec342f5e424f5147c48d)
- use my toHex [`76ffe66`](https://github.com/browserify/sha.js/commit/76ffe66ba119e9e8fc88d6a5b5dcd5ffabf2a9da)
- didin't work [`254a4e8`](https://github.com/browserify/sha.js/commit/254a4e8c3e4fe79c8ce4cacd23a91426e9d8f32f)
- always run all tests [`18f39f8`](https://github.com/browserify/sha.js/commit/18f39f8e96ddebd86d32604b55c388e0c72306e1)
- remove hexpp [`e7f3030`](https://github.com/browserify/sha.js/commit/e7f30308c64bfc5f00a9b8ccf4c17ae65a3ddc55)
- make installable as a command [`f6842dd`](https://github.com/browserify/sha.js/commit/f6842dde37f0b37042e513fc63d214989417fff4)
- 0.11 is not working... [`a6aacc6`](https://github.com/browserify/sha.js/commit/a6aacc66f417ee17a4363cc1d20350bfa7a682cc)
