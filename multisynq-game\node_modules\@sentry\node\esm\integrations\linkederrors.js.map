{"version": 3, "file": "linkederrors.js", "sourceRoot": "", "sources": ["../../src/integrations/linkederrors.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,uBAAuB,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAEtE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE1D,OAAO,EAAE,qBAAqB,EAAE,MAAM,YAAY,CAAC;AAEnD,IAAM,WAAW,GAAG,OAAO,CAAC;AAC5B,IAAM,aAAa,GAAG,CAAC,CAAC;AAExB,iCAAiC;AACjC;IAqBE;;OAEG;IACH,sBAAmB,OAA8C;QAA9C,wBAAA,EAAA,YAA8C;QAlBjE;;WAEG;QACa,SAAI,GAAW,YAAY,CAAC,EAAE,CAAC;QAgB7C,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,IAAI,WAAW,CAAC;QACvC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,IAAI,aAAa,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,gCAAS,GAAhB;QACE,uBAAuB,CAAC,UAAC,KAAY,EAAE,IAAgB;YACrD,IAAM,IAAI,GAAG,aAAa,EAAE,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC1D,IAAI,IAAI,EAAE;gBACR,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1D,OAAO,OAAO,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;aACrE;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,+BAAQ,GAAhB,UAAiB,KAAY,EAAE,IAAgB;QAA/C,iBAiBC;QAhBC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,EAAE;YACxG,OAAO,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACnC;QAED,OAAO,IAAI,WAAW,CAAQ,UAAA,OAAO;YACnC,KAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAA0B,EAAE,KAAI,CAAC,IAAI,CAAC;iBAC5D,IAAI,CAAC,UAAC,YAAyB;gBAC9B,IAAI,KAAK,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE;oBACtD,KAAK,CAAC,SAAS,CAAC,MAAM,YAAO,YAAY,EAAK,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;iBACvE;gBACD,OAAO,CAAC,KAAK,CAAC,CAAC;YACjB,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,EAAE;gBACV,OAAO,CAAC,KAAK,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,qCAAc,GAAtB,UAAuB,KAAoB,EAAE,GAAW,EAAE,KAAuB;QAAjF,iBAiBC;QAjByD,sBAAA,EAAA,UAAuB;QAC/E,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;YACvE,OAAO,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACnC;QACD,OAAO,IAAI,WAAW,CAAc,UAAC,OAAO,EAAE,MAAM;YAClD,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;iBAC9B,IAAI,CAAC,UAAC,SAAoB;gBACzB,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,YAAG,SAAS,GAAK,KAAK,EAAE;qBACxD,IAAI,CAAC,OAAO,CAAC;qBACb,IAAI,CAAC,IAAI,EAAE;oBACV,MAAM,EAAE,CAAC;gBACX,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,EAAE;gBACV,MAAM,EAAE,CAAC;YACX,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IApFD;;OAEG;IACW,eAAE,GAAW,cAAc,CAAC;IAkF5C,mBAAC;CAAA,AAtFD,IAsFC;SAtFY,YAAY", "sourcesContent": ["import { addGlobalEventProcessor, getCurrentHub } from '@sentry/core';\nimport { Event, EventHint, Exception, ExtendedError, Integration } from '@sentry/types';\nimport { isInstanceOf, SyncPromise } from '@sentry/utils';\n\nimport { getExceptionFromError } from '../parsers';\n\nconst DEFAULT_KEY = 'cause';\nconst DEFAULT_LIMIT = 5;\n\n/** Adds SDK info to an event. */\nexport class LinkedErrors implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'LinkedErrors';\n\n  /**\n   * @inheritDoc\n   */\n  public readonly name: string = LinkedErrors.id;\n\n  /**\n   * @inheritDoc\n   */\n  private readonly _key: string;\n\n  /**\n   * @inheritDoc\n   */\n  private readonly _limit: number;\n\n  /**\n   * @inheritDoc\n   */\n  public constructor(options: { key?: string; limit?: number } = {}) {\n    this._key = options.key || DEFAULT_KEY;\n    this._limit = options.limit || DEFAULT_LIMIT;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(): void {\n    addGlobalEventProcessor((event: Event, hint?: EventHint) => {\n      const self = getCurrentHub().getIntegration(LinkedErrors);\n      if (self) {\n        const handler = self._handler && self._handler.bind(self);\n        return typeof handler === 'function' ? handler(event, hint) : event;\n      }\n      return event;\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  private _handler(event: Event, hint?: EventHint): PromiseLike<Event> {\n    if (!event.exception || !event.exception.values || !hint || !isInstanceOf(hint.originalException, Error)) {\n      return SyncPromise.resolve(event);\n    }\n\n    return new SyncPromise<Event>(resolve => {\n      this._walkErrorTree(hint.originalException as Error, this._key)\n        .then((linkedErrors: Exception[]) => {\n          if (event && event.exception && event.exception.values) {\n            event.exception.values = [...linkedErrors, ...event.exception.values];\n          }\n          resolve(event);\n        })\n        .then(null, () => {\n          resolve(event);\n        });\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  private _walkErrorTree(error: ExtendedError, key: string, stack: Exception[] = []): PromiseLike<Exception[]> {\n    if (!isInstanceOf(error[key], Error) || stack.length + 1 >= this._limit) {\n      return SyncPromise.resolve(stack);\n    }\n    return new SyncPromise<Exception[]>((resolve, reject) => {\n      getExceptionFromError(error[key])\n        .then((exception: Exception) => {\n          this._walkErrorTree(error[key], key, [exception, ...stack])\n            .then(resolve)\n            .then(null, () => {\n              reject();\n            });\n        })\n        .then(null, () => {\n          reject();\n        });\n    });\n  }\n}\n"]}