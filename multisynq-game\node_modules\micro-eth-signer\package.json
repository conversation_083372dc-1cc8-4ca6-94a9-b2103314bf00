{"name": "micro-eth-signer", "version": "0.14.0", "description": "Minimal library for Ethereum transactions, addresses and smart contracts", "files": ["*.js", "*.d.ts", "*.js.map", "*.d.ts.map", "abi", "esm", "net", "src"], "main": "index.js", "module": "esm/index.js", "types": "esm/index.d.ts", "dependencies": {"@noble/curves": "~1.8.1", "@noble/hashes": "~1.7.1", "micro-packed": "~0.7.2"}, "devDependencies": {"@paulmillr/jsbt": "0.3.3", "@paulmillr/trusted-setups": "0.1.2", "micro-bmark": "0.4.0", "micro-ftch": "0.4.0", "micro-should": "0.5.1", "prettier": "3.5.2", "snappyjs": "0.7.0", "typescript": "5.8.2", "yaml": "2.4.1"}, "author": "<PERSON> (https://paulmillr.com)", "repository": {"type": "git", "url": "git+https://github.com/paulmillr/micro-eth-signer.git"}, "license": "MIT", "exports": {".": {"import": "./esm/index.js", "require": "./index.js"}, "./abi": {"import": "./esm/abi/index.js", "require": "./abi/index.js"}, "./address": {"import": "./esm/address.js", "require": "./address.js"}, "./kzg": {"import": "./esm/kzg.js", "require": "./kzg.js"}, "./net": {"import": "./esm/net/index.js", "require": "./net/index.js"}, "./rlp": {"import": "./esm/rlp.js", "require": "./rlp.js"}, "./ssz": {"import": "./esm/ssz.js", "require": "./ssz.js"}, "./tx": {"import": "./esm/tx.js", "require": "./tx.js"}, "./typed-data": {"import": "./esm/typed-data.js", "require": "./typed-data.js"}, "./verkle": {"import": "./esm/verkle.js", "require": "./verkle.js"}, "./utils": {"import": "./esm/utils.js", "require": "./utils.js"}}, "sideEffects": false, "keywords": ["ethereum", "eth", "create", "sign", "validate", "transaction", "address", "tx", "web3", "ethers", "micro", "nano", "signer"], "scripts": {"build": "tsc && tsc -p tsconfig.cjs.json", "build:release": "npx jsbt esbuild test/build", "bench": "node benchmark/tx.js", "lint": "prettier --check src", "format": "prettier --write src", "test": "node test/index.js", "test:bun": "bun test/index.js", "test:deno": "deno --allow-env --allow-read test/index.js"}}