{"version": 3, "file": "status.js", "sourceRoot": "", "sources": ["../src/status.ts"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,yCAAyC;AACzC,MAAM,CAAN,IAAY,MAaX;AAbD,WAAY,MAAM;IAChB,0CAA0C;IAC1C,6BAAmB,CAAA;IACnB,+DAA+D;IAC/D,6BAAmB,CAAA;IACnB,iDAAiD;IACjD,6BAAmB,CAAA;IACnB,qEAAqE;IACrE,kCAAwB,CAAA;IACxB,wCAAwC;IACxC,6BAAmB,CAAA;IACnB,qDAAqD;IACrD,2BAAiB,CAAA;AACnB,CAAC,EAbW,MAAM,KAAN,MAAM,QAajB;AAED,0EAA0E;AAC1E,WAAiB,MAAM;IACrB;;;;;OAKG;IACH,SAAgB,YAAY,CAAC,IAAY;QACvC,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,EAAE;YAC7B,OAAO,MAAM,CAAC,OAAO,CAAC;SACvB;QAED,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,OAAO,MAAM,CAAC,SAAS,CAAC;SACzB;QAED,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,EAAE;YAC7B,OAAO,MAAM,CAAC,OAAO,CAAC;SACvB;QAED,IAAI,IAAI,IAAI,GAAG,EAAE;YACf,OAAO,MAAM,CAAC,MAAM,CAAC;SACtB;QAED,OAAO,MAAM,CAAC,OAAO,CAAC;IACxB,CAAC;IAlBe,mBAAY,eAkB3B,CAAA;AACH,CAAC,EA1BgB,MAAM,KAAN,MAAM,QA0BtB", "sourcesContent": ["/** The status of an event. */\n// eslint-disable-next-line import/export\nexport enum Status {\n  /** The status could not be determined. */\n  Unknown = 'unknown',\n  /** The event was skipped due to configuration or callbacks. */\n  Skipped = 'skipped',\n  /** The event was sent to Sentry successfully. */\n  Success = 'success',\n  /** The client is currently rate limited and will try again later. */\n  RateLimit = 'rate_limit',\n  /** The event could not be processed. */\n  Invalid = 'invalid',\n  /** A server-side error ocurred during submission. */\n  Failed = 'failed',\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace, import/export\nexport namespace Status {\n  /**\n   * Converts a HTTP status code into a {@link Status}.\n   *\n   * @param code The HTTP response status code.\n   * @returns The send status or {@link Status.Unknown}.\n   */\n  export function fromHttpCode(code: number): Status {\n    if (code >= 200 && code < 300) {\n      return Status.Success;\n    }\n\n    if (code === 429) {\n      return Status.RateLimit;\n    }\n\n    if (code >= 400 && code < 500) {\n      return Status.Invalid;\n    }\n\n    if (code >= 500) {\n      return Status.Failed;\n    }\n\n    return Status.Unknown;\n  }\n}\n"]}