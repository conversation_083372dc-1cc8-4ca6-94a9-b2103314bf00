{"version": 3, "file": "request.js", "sourceRoot": "", "sources": ["../../src/browser/request.ts"], "names": [], "mappings": ";;AAAA,mCAA4C;AAC5C,uCAA2F;AAG3F,kCAAmE;AAEtD,QAAA,uBAAuB,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AAwE/C,QAAA,oCAAoC,GAAkC;IACjF,UAAU,EAAE,IAAI;IAChB,QAAQ,EAAE,IAAI;IACd,cAAc,EAAE,+BAAuB;CACxC,CAAC;AAEF,0DAA0D;AAC1D,SAAgB,8BAA8B,CAAC,QAAiD;IAC9F,6DAA6D;IACvD,IAAA,mGAGL,EAHO,0BAAU,EAAE,sBAAQ,EAAE,kCAAc,EAAE,0DAG7C,CAAC;IAEF,mEAAmE;IACnE,wCAAwC;IACxC,IAAM,MAAM,GAA4B,EAAE,CAAC;IAE3C,IAAM,uBAAuB,GAAG,UAAC,GAAW;QAC1C,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE;YACf,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;SACpB;QACD,IAAM,OAAO,GAAG,cAAc,CAAC;QAC/B,MAAM,CAAC,GAAG,CAAC;YACT,OAAO,CAAC,IAAI,CAAC,UAAC,MAAuB,IAAK,OAAA,yBAAiB,CAAC,GAAG,EAAE,MAAM,CAAC,EAA9B,CAA8B,CAAC;gBACzE,CAAC,yBAAiB,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;QACxC,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC,CAAC;IAEF,0FAA0F;IAC1F,sEAAsE;IACtE,IAAI,gBAAgB,GAAG,uBAAuB,CAAC;IAC/C,IAAI,OAAO,0BAA0B,KAAK,UAAU,EAAE;QACpD,gBAAgB,GAAG,UAAC,GAAW;YAC7B,OAAO,uBAAuB,CAAC,GAAG,CAAC,IAAI,0BAA0B,CAAC,GAAG,CAAC,CAAC;QACzE,CAAC,CAAC;KACH;IAED,IAAM,KAAK,GAAyB,EAAE,CAAC;IAEvC,IAAI,UAAU,EAAE;QACd,iCAAyB,CAAC;YACxB,QAAQ,EAAE,UAAC,WAAsB;gBAC/B,aAAa,CAAC,WAAW,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;YACtD,CAAC;YACD,IAAI,EAAE,OAAO;SACd,CAAC,CAAC;KACJ;IAED,IAAI,QAAQ,EAAE;QACZ,iCAAyB,CAAC;YACxB,QAAQ,EAAE,UAAC,WAAoB;gBAC7B,WAAW,CAAC,WAAW,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;YACpD,CAAC;YACD,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;KACJ;AACH,CAAC;AAlDD,wEAkDC;AAED;;GAEG;AACH,SAAgB,aAAa,CAC3B,WAAsB,EACtB,gBAA0C,EAC1C,KAA2B;;IAE3B,IAAM,oBAAoB,SAAG,mBAAa,EAAE;SACzC,SAAS,EAAE,0CACV,UAAU,EAAE,CAAC;IACjB,IACE,CAAC,CAAC,oBAAoB,IAAI,yBAAiB,CAAC,oBAAoB,CAAC,CAAC;QAClE,CAAC,CAAC,WAAW,CAAC,SAAS,IAAI,gBAAgB,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EACvE;QACA,OAAO;KACR;IAED,IAAI,WAAW,CAAC,YAAY,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE;QAC5D,IAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,IAAI,EAAE;YACR,IAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;YACtC,IAAI,QAAQ,EAAE;gBACZ,sDAAsD;gBACtD,sEAAsE;gBACtE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;aACrC;YACD,IAAI,CAAC,MAAM,EAAE,CAAC;YAEd,gEAAgE;YAChE,OAAO,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;SAC5C;QACD,OAAO;KACR;IAED,IAAM,iBAAiB,GAAG,4BAAoB,EAAE,CAAC;IACjD,IAAI,iBAAiB,EAAE;QACrB,IAAM,IAAI,GAAG,iBAAiB,CAAC,UAAU,CAAC;YACxC,IAAI,wCACC,WAAW,CAAC,SAAS,KACxB,IAAI,EAAE,OAAO,GACd;YACD,WAAW,EAAK,WAAW,CAAC,SAAS,CAAC,MAAM,SAAI,WAAW,CAAC,SAAS,CAAC,GAAK;YAC3E,EAAE,EAAE,MAAM;SACX,CAAC,CAAC;QAEH,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3C,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QAE1B,IAAM,OAAO,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAqB,CAAC,CAAC;QAChF,8DAA8D;QAC9D,IAAM,OAAO,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,GAAI,WAAW,CAAC,IAAI,CAAC,CAAC,CAA4B,IAAI,EAAE,CAAC,CAAC;QAC9F,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC9B,IAAI,oBAAY,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;YAClC,OAAO,GAAI,OAAmB,CAAC,OAAO,CAAC;SACxC;QACD,IAAI,OAAO,EAAE;YACX,sEAAsE;YACtE,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,UAAU,EAAE;gBACxC,sEAAsE;gBACtE,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;aACtD;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACjC,OAAO,oBAAO,OAAO,GAAE,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,EAAC,CAAC;aAChE;iBAAM;gBACL,OAAO,yCAAQ,OAAO,KAAE,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,GAAE,CAAC;aAChE;SACF;aAAM;YACL,OAAO,GAAG,EAAE,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;SACpD;QACD,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;KAC3B;AACH,CAAC;AApED,sCAoEC;AAED;;GAEG;AACH,SAAgB,WAAW,CACzB,WAAoB,EACpB,gBAA0C,EAC1C,KAA2B;;IAE3B,IAAM,oBAAoB,SAAG,mBAAa,EAAE;SACzC,SAAS,EAAE,0CACV,UAAU,EAAE,CAAC;IACjB,IACE,CAAC,CAAC,oBAAoB,IAAI,yBAAiB,CAAC,oBAAoB,CAAC,CAAC;QAClE,CAAC,CAAC,WAAW,CAAC,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,cAAc,IAAI,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAC5G,WAAW,CAAC,GAAG,CAAC,sBAAsB,EACtC;QACA,OAAO;KACR;IAED,IAAM,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC;IAE3C,kGAAkG;IAClG,IAAI,WAAW,CAAC,YAAY,IAAI,WAAW,CAAC,GAAG,CAAC,sBAAsB,EAAE;QACtE,IAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAC3D,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YACpC,IAAI,CAAC,MAAM,EAAE,CAAC;YAEd,gEAAgE;YAChE,OAAO,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;SACtD;QACD,OAAO;KACR;IAED,wCAAwC;IACxC,IAAM,iBAAiB,GAAG,4BAAoB,EAAE,CAAC;IACjD,IAAI,iBAAiB,EAAE;QACrB,IAAM,IAAI,GAAG,iBAAiB,CAAC,UAAU,CAAC;YACxC,IAAI,wCACC,GAAG,CAAC,IAAI,KACX,IAAI,EAAE,KAAK,EACX,MAAM,EAAE,GAAG,CAAC,MAAM,EAClB,GAAG,EAAE,GAAG,CAAC,GAAG,GACb;YACD,WAAW,EAAK,GAAG,CAAC,MAAM,SAAI,GAAG,CAAC,GAAK;YACvC,EAAE,EAAE,MAAM;SACX,CAAC,CAAC;QAEH,WAAW,CAAC,GAAG,CAAC,sBAAsB,GAAG,IAAI,CAAC,MAAM,CAAC;QACrD,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,IAAI,CAAC;QAErD,IAAI,WAAW,CAAC,GAAG,CAAC,gBAAgB,EAAE;YACpC,IAAI;gBACF,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;aACxE;YAAC,OAAO,CAAC,EAAE;gBACV,yHAAyH;aAC1H;SACF;KACF;AACH,CAAC;AAxDD,kCAwDC", "sourcesContent": ["import { getCurrentHub } from '@sentry/hub';\nimport { addInstrumentationHandler, isInstanceOf, isMatchingPattern } from '@sentry/utils';\n\nimport { Span } from '../span';\nimport { getActiveTransaction, hasTracingEnabled } from '../utils';\n\nexport const DEFAULT_TRACING_ORIGINS = ['localhost', /^\\//];\n\n/** Options for Request Instrumentation */\nexport interface RequestInstrumentationOptions {\n  /**\n   * List of strings / regex where the integration should create Spans out of. Additionally this will be used\n   * to define which outgoing requests the `sentry-trace` header will be attached to.\n   *\n   * Default: ['localhost', /^\\//] {@see DEFAULT_TRACING_ORIGINS}\n   */\n  tracingOrigins: Array<string | RegExp>;\n\n  /**\n   * Flag to disable patching all together for fetch requests.\n   *\n   * Default: true\n   */\n  traceFetch: boolean;\n\n  /**\n   * Flag to disable patching all together for xhr requests.\n   *\n   * Default: true\n   */\n  traceXHR: boolean;\n\n  /**\n   * This function will be called before creating a span for a request with the given url.\n   * Return false if you don't want a span for the given url.\n   *\n   * By default it uses the `tracingOrigins` options as a url match.\n   */\n  shouldCreateSpanForRequest?(url: string): boolean;\n}\n\n/** Data returned from fetch callback */\nexport interface FetchData {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  args: any[]; // the arguments passed to the fetch call itself\n  fetchData?: {\n    method: string;\n    url: string;\n    // span_id\n    __span?: string;\n  };\n\n  // TODO Should this be unknown instead? If we vendor types, make it a Response\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  response?: any;\n\n  startTimestamp: number;\n  endTimestamp?: number;\n}\n\n/** Data returned from XHR request */\nexport interface XHRData {\n  xhr?: {\n    __sentry_xhr__?: {\n      method: string;\n      url: string;\n      status_code: number;\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      data: Record<string, any>;\n    };\n    __sentry_xhr_span_id__?: string;\n    setRequestHeader?: (key: string, val: string) => void;\n    __sentry_own_request__?: boolean;\n  };\n  startTimestamp: number;\n  endTimestamp?: number;\n}\n\nexport const defaultRequestInstrumentationOptions: RequestInstrumentationOptions = {\n  traceFetch: true,\n  traceXHR: true,\n  tracingOrigins: DEFAULT_TRACING_ORIGINS,\n};\n\n/** Registers span creators for xhr and fetch requests  */\nexport function registerRequestInstrumentation(_options?: Partial<RequestInstrumentationOptions>): void {\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  const { traceFetch, traceXHR, tracingOrigins, shouldCreateSpanForRequest } = {\n    ...defaultRequestInstrumentationOptions,\n    ..._options,\n  };\n\n  // We should cache url -> decision so that we don't have to compute\n  // regexp everytime we create a request.\n  const urlMap: Record<string, boolean> = {};\n\n  const defaultShouldCreateSpan = (url: string): boolean => {\n    if (urlMap[url]) {\n      return urlMap[url];\n    }\n    const origins = tracingOrigins;\n    urlMap[url] =\n      origins.some((origin: string | RegExp) => isMatchingPattern(url, origin)) &&\n      !isMatchingPattern(url, 'sentry_key');\n    return urlMap[url];\n  };\n\n  // We want that our users don't have to re-implement shouldCreateSpanForRequest themselves\n  // That's why we filter out already unwanted Spans from tracingOrigins\n  let shouldCreateSpan = defaultShouldCreateSpan;\n  if (typeof shouldCreateSpanForRequest === 'function') {\n    shouldCreateSpan = (url: string) => {\n      return defaultShouldCreateSpan(url) && shouldCreateSpanForRequest(url);\n    };\n  }\n\n  const spans: Record<string, Span> = {};\n\n  if (traceFetch) {\n    addInstrumentationHandler({\n      callback: (handlerData: FetchData) => {\n        fetchCallback(handlerData, shouldCreateSpan, spans);\n      },\n      type: 'fetch',\n    });\n  }\n\n  if (traceXHR) {\n    addInstrumentationHandler({\n      callback: (handlerData: XHRData) => {\n        xhrCallback(handlerData, shouldCreateSpan, spans);\n      },\n      type: 'xhr',\n    });\n  }\n}\n\n/**\n * Create and track fetch request spans\n */\nexport function fetchCallback(\n  handlerData: FetchData,\n  shouldCreateSpan: (url: string) => boolean,\n  spans: Record<string, Span>,\n): void {\n  const currentClientOptions = getCurrentHub()\n    .getClient()\n    ?.getOptions();\n  if (\n    !(currentClientOptions && hasTracingEnabled(currentClientOptions)) ||\n    !(handlerData.fetchData && shouldCreateSpan(handlerData.fetchData.url))\n  ) {\n    return;\n  }\n\n  if (handlerData.endTimestamp && handlerData.fetchData.__span) {\n    const span = spans[handlerData.fetchData.__span];\n    if (span) {\n      const response = handlerData.response;\n      if (response) {\n        // TODO (kmclb) remove this once types PR goes through\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        span.setHttpStatus(response.status);\n      }\n      span.finish();\n\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete spans[handlerData.fetchData.__span];\n    }\n    return;\n  }\n\n  const activeTransaction = getActiveTransaction();\n  if (activeTransaction) {\n    const span = activeTransaction.startChild({\n      data: {\n        ...handlerData.fetchData,\n        type: 'fetch',\n      },\n      description: `${handlerData.fetchData.method} ${handlerData.fetchData.url}`,\n      op: 'http',\n    });\n\n    handlerData.fetchData.__span = span.spanId;\n    spans[span.spanId] = span;\n\n    const request = (handlerData.args[0] = handlerData.args[0] as string | Request);\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const options = (handlerData.args[1] = (handlerData.args[1] as { [key: string]: any }) || {});\n    let headers = options.headers;\n    if (isInstanceOf(request, Request)) {\n      headers = (request as Request).headers;\n    }\n    if (headers) {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      if (typeof headers.append === 'function') {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        headers.append('sentry-trace', span.toTraceparent());\n      } else if (Array.isArray(headers)) {\n        headers = [...headers, ['sentry-trace', span.toTraceparent()]];\n      } else {\n        headers = { ...headers, 'sentry-trace': span.toTraceparent() };\n      }\n    } else {\n      headers = { 'sentry-trace': span.toTraceparent() };\n    }\n    options.headers = headers;\n  }\n}\n\n/**\n * Create and track xhr request spans\n */\nexport function xhrCallback(\n  handlerData: XHRData,\n  shouldCreateSpan: (url: string) => boolean,\n  spans: Record<string, Span>,\n): void {\n  const currentClientOptions = getCurrentHub()\n    .getClient()\n    ?.getOptions();\n  if (\n    !(currentClientOptions && hasTracingEnabled(currentClientOptions)) ||\n    !(handlerData.xhr && handlerData.xhr.__sentry_xhr__ && shouldCreateSpan(handlerData.xhr.__sentry_xhr__.url)) ||\n    handlerData.xhr.__sentry_own_request__\n  ) {\n    return;\n  }\n\n  const xhr = handlerData.xhr.__sentry_xhr__;\n\n  // check first if the request has finished and is tracked by an existing span which should now end\n  if (handlerData.endTimestamp && handlerData.xhr.__sentry_xhr_span_id__) {\n    const span = spans[handlerData.xhr.__sentry_xhr_span_id__];\n    if (span) {\n      span.setHttpStatus(xhr.status_code);\n      span.finish();\n\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete spans[handlerData.xhr.__sentry_xhr_span_id__];\n    }\n    return;\n  }\n\n  // if not, create a new span to track it\n  const activeTransaction = getActiveTransaction();\n  if (activeTransaction) {\n    const span = activeTransaction.startChild({\n      data: {\n        ...xhr.data,\n        type: 'xhr',\n        method: xhr.method,\n        url: xhr.url,\n      },\n      description: `${xhr.method} ${xhr.url}`,\n      op: 'http',\n    });\n\n    handlerData.xhr.__sentry_xhr_span_id__ = span.spanId;\n    spans[handlerData.xhr.__sentry_xhr_span_id__] = span;\n\n    if (handlerData.xhr.setRequestHeader) {\n      try {\n        handlerData.xhr.setRequestHeader('sentry-trace', span.toTraceparent());\n      } catch (_) {\n        // Error: InvalidStateError: Failed to execute 'setRequestHeader' on 'XMLHttpRequest': The object's state must be OPENED.\n      }\n    }\n  }\n}\n"]}