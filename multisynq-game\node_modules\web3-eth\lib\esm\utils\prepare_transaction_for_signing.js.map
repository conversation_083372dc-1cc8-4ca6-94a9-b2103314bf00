{"version": 3, "file": "prepare_transaction_for_signing.js", "sourceRoot": "", "sources": ["../../../src/utils/prepare_transaction_for_signing.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;;;;;;;;;;AAEF,OAAO,EASN,eAAe,GACf,MAAM,YAAY,CAAC;AAEpB,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,kBAAkB,EAAa,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC1E,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAE,6BAA6B,EAAE,MAAM,kBAAkB,CAAC;AACjE,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAE9D,MAAM,kCAAkC,GAAG,CAC1C,WAA6E,EAC5E,EAAE;;IAAC,OAAA,iCACD,WAAW,KACd,KAAK,EAAE,WAAW,CAAC,KAAK,EACxB,QAAQ,EAAE,WAAW,CAAC,QAAQ,EAC9B,QAAQ,EAAE,MAAA,WAAW,CAAC,QAAQ,mCAAI,WAAW,CAAC,GAAG,EACjD,EAAE,EAAE,WAAW,CAAC,EAAE,EAClB,KAAK,EAAE,WAAW,CAAC,KAAK,EACxB,IAAI,EAAE,MAAA,WAAW,CAAC,IAAI,mCAAI,WAAW,CAAC,KAAK,EAC3C,IAAI,EAAE,WAAW,CAAC,IAAI,EACtB,OAAO,EAAE,WAAW,CAAC,OAAO,EAC5B,UAAU,EACT,WACA,CAAC,UAAU,EACZ,oBAAoB,EACnB,WACA,CAAC,oBAAoB,EACtB,YAAY,EACX,WACA,CAAC,YAAY,IACb,CAAA;CAAA,CAAC;AAEH,MAAM,+BAA+B,GAAG,CACvC,WAA6E,EAC7E,WAAyC,EACxC,EAAE;;IACH,MAAM,4BAA4B,GACjC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACnE,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAEhC,IAAI,MAAM,CAAC;IACX,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACnC,2CAA2C;QAC3C,IAAI,WAAW,CAAC,aAAa,EAAE,CAAC;YAC/B,MAAM,qBAAQ,WAAW,CAAC,aAAa,CAAE,CAAC;YAE1C,IAAI,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;gBAC7B,MAAM,CAAC,QAAQ,GAAG,MAAA,WAAW,CAAC,QAAQ,mCAAI,WAAW,CAAC,eAAe,CAAC;YACvE,IAAI,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC;gBAC9B,MAAM,CAAC,SAAS,GAAG,WAAW,CAAC,YAA2B,CAAC;QAC7D,CAAC;aAAM,CAAC;YACP,MAAM,GAAG,MAAM,CAAC,MAAM,CACrB;gBACC,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAW;gBAChD,SAAS,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC;oBAC3C,CAAC,CAAE,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAY;oBAC7C,CAAC,CAAC,SAAS;gBACZ,eAAe,EAAE,MAAA,WAAW,CAAC,QAAQ,mCAAI,WAAW,CAAC,eAAe;aACpE,EACD;gBACC,SAAS,EAAE,WAAW,CAAC,YAAY;aACnC,CACD,CAAC;QACH,CAAC;IACF,CAAC;SAAM,CAAC;QACP,MAAM,IAAI,GACT,MAAA,MAAA,MAAA,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,MAAM,0CAAE,WAAW,0CAAE,IAAI,mCAAI,WAAW,CAAC,KAAK,mCAAI,gBAAgB,CAAC;QACjF,MAAM,OAAO,GAAG,QAAQ,CACvB,MAAA,MAAA,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,MAAM,0CAAE,WAAW,0CAAE,OAAO,mCAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,OAAO,CACvD,CAAC;QACZ,MAAM,SAAS,GAAG,QAAQ,CACzB,MAAA,MAAA,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,MAAM,0CAAE,WAAW,0CAAE,SAAS,mCAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,SAAS,CAC3D,CAAC;QACZ,MAAM,eAAe,GACpB,MAAA,MAAA,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,MAAM,0CAAE,QAAQ,mCAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,QAAQ,mCAAI,WAAW,CAAC,eAAe,CAAC;QACvF,MAAM,SAAS,GACd,MAAA,MAAA,MAAA,WAAW,CAAC,MAAM,0CAAE,SAAS,mCAAI,WAAW,CAAC,KAAK,mCAAI,WAAW,CAAC,YAAY,CAAC;QAEhF,IAAI,OAAO,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;YAClC,MAAM,GAAG,MAAM,CAAC,MAAM,CACrB;gBACC,IAAI;gBACJ,OAAO;gBACP,SAAS;gBACT,eAAe;aACf,EACD;gBACC,SAAS;aACT,CACD,CAAC;QACH,CAAC;IACF,CAAC;IACD,OAAO,EAAE,MAAM,EAAe,CAAC;AAChC,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,4BAA4B,GAAG,wDAM1C,EAAE,+FALH,WAAwB,EACxB,WAAyC,EACzC,UAAmC,EACnC,YAAY,GAAG,KAAK,EACpB,YAAY,GAAG,IAAI;IAEnB,MAAM,oBAAoB,GAAG,CAAC,MAAM,kBAAkB,CAAC;QACtD,WAAW;QACX,WAAW;QACX,UAAU;QACV,YAAY;QACZ,YAAY;KACZ,CAAC,CAA4C,CAAC;IAC/C,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,oBAAoB,EAAE,eAAe,EAAE;QACrF,iBAAiB,EAAE,WAAW,CAAC,MAAM,CAAC,uBAAuB;KAC7D,CAAgF,CAAC;IAElF,6BAA6B,CAC5B,oBAAkF,EAClF,SAAS,EACT;QACC,iBAAiB,EAAE,WAAW,CAAC,MAAM,CAAC,uBAAuB;KAC7D,CACD,CAAC;IAEF,OAAO,kBAAkB,CAAC,UAAU,CACnC,kCAAkC,CAAC,oBAAoB,CAAC,EACxD,+BAA+B,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAClE,CAAC;AACH,CAAC,CAAA,CAAC"}