{"version": 3, "file": "https.js", "sourceRoot": "", "sources": ["../../src/transports/https.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,aAAa,EAAE,MAAM,QAAQ,CAAC;AAEvC,kCAAkC;AAClC;IAAoC,kCAAa;IAC/C,+CAA+C;IAC/C,wBAA0B,OAAyB;QAAnD,YACE,kBAAM,OAAO,CAAC,SAMf;QAPyB,aAAO,GAAP,OAAO,CAAkB;QAEjD,IAAM,KAAK,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;QAC3G,KAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,KAAI,CAAC,MAAM,GAAG,KAAK;YACjB,CAAC,CAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,KAAK,CAAiB;YAC5D,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;;IAC3E,CAAC;IAED;;OAEG;IACI,kCAAS,GAAhB,UAAiB,KAAY;QAC3B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,WAAW,CAAC,uCAAuC,CAAC,CAAC;SAChE;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IACH,qBAAC;AAAD,CAAC,AApBD,CAAoC,aAAa,GAoBhD", "sourcesContent": ["import { Event, Response, TransportOptions } from '@sentry/types';\nimport { SentryError } from '@sentry/utils';\nimport * as https from 'https';\n\nimport { BaseTransport } from './base';\n\n/** Node https module transport */\nexport class HTTPSTransport extends BaseTransport {\n  /** Create a new instance and set this.agent */\n  public constructor(public options: TransportOptions) {\n    super(options);\n    const proxy = options.httpsProxy || options.httpProxy || process.env.https_proxy || process.env.http_proxy;\n    this.module = https;\n    this.client = proxy\n      ? (new (require('https-proxy-agent'))(proxy) as https.Agent)\n      : new https.Agent({ keepAlive: false, maxSockets: 30, timeout: 2000 });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendEvent(event: Event): Promise<Response> {\n    if (!this.module) {\n      throw new SentryError('No module available in HTTPSTransport');\n    }\n    return this._sendWithModule(this.module, event);\n  }\n}\n"]}