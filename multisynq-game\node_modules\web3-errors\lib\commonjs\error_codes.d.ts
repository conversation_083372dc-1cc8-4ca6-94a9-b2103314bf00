export declare const ERR_RESPONSE = 100;
export declare const ERR_INVALID_RESPONSE = 101;
export declare const ERR_PARAM = 200;
export declare const ERR_FORMATTERS = 201;
export declare const ERR_METHOD_NOT_IMPLEMENTED = 202;
export declare const ERR_OPERATION_TIMEOUT = 203;
export declare const ERR_OPERATION_ABORT = 204;
export declare const ERR_ABI_ENCODING = 205;
export declare const ERR_EXISTING_PLUGIN_NAMESPACE = 206;
export declare const ERR_INVALID_METHOD_PARAMS = 207;
export declare const ERR_MULTIPLE_ERRORS = 208;
export declare const ERR_CONTRACT = 300;
export declare const ERR_CONTRACT_RESOLVER_MISSING = 301;
export declare const ERR_CONTRACT_ABI_MISSING = 302;
export declare const ERR_CONTRACT_REQUIRED_CALLBACK = 303;
export declare const ERR_CONTRACT_EVENT_NOT_EXISTS = 304;
export declare const ERR_CONTRACT_RESERVED_EVENT = 305;
export declare const ERR_CONTRACT_MISSING_DEPLOY_DATA = 306;
export declare const ERR_CONTRACT_MISSING_ADDRESS = 307;
export declare const ERR_CONTRACT_MISSING_FROM_ADDRESS = 308;
export declare const ERR_CONTRACT_INSTANTIATION = 309;
export declare const ERR_CONTRACT_EXECUTION_REVERTED = 310;
export declare const ERR_CONTRACT_TX_DATA_AND_INPUT = 311;
export declare const ERR_TX = 400;
export declare const ERR_TX_REVERT_INSTRUCTION = 401;
export declare const ERR_TX_REVERT_TRANSACTION = 402;
export declare const ERR_TX_NO_CONTRACT_ADDRESS = 403;
export declare const ERR_TX_CONTRACT_NOT_STORED = 404;
export declare const ERR_TX_REVERT_WITHOUT_REASON = 405;
export declare const ERR_TX_OUT_OF_GAS = 406;
export declare const ERR_RAW_TX_UNDEFINED = 407;
export declare const ERR_TX_INVALID_SENDER = 408;
export declare const ERR_TX_INVALID_CALL = 409;
export declare const ERR_TX_MISSING_CUSTOM_CHAIN = 410;
export declare const ERR_TX_MISSING_CUSTOM_CHAIN_ID = 411;
export declare const ERR_TX_CHAIN_ID_MISMATCH = 412;
export declare const ERR_TX_INVALID_CHAIN_INFO = 413;
export declare const ERR_TX_MISSING_CHAIN_INFO = 414;
export declare const ERR_TX_MISSING_GAS = 415;
export declare const ERR_TX_INVALID_LEGACY_GAS = 416;
export declare const ERR_TX_INVALID_FEE_MARKET_GAS = 417;
export declare const ERR_TX_INVALID_FEE_MARKET_GAS_PRICE = 418;
export declare const ERR_TX_INVALID_LEGACY_FEE_MARKET = 419;
export declare const ERR_TX_INVALID_OBJECT = 420;
export declare const ERR_TX_INVALID_NONCE_OR_CHAIN_ID = 421;
export declare const ERR_TX_UNABLE_TO_POPULATE_NONCE = 422;
export declare const ERR_TX_UNSUPPORTED_EIP_1559 = 423;
export declare const ERR_TX_UNSUPPORTED_TYPE = 424;
export declare const ERR_TX_DATA_AND_INPUT = 425;
export declare const ERR_TX_POLLING_TIMEOUT = 426;
export declare const ERR_TX_RECEIPT_MISSING_OR_BLOCKHASH_NULL = 427;
export declare const ERR_TX_RECEIPT_MISSING_BLOCK_NUMBER = 428;
export declare const ERR_TX_LOCAL_WALLET_NOT_AVAILABLE = 429;
export declare const ERR_TX_NOT_FOUND = 430;
export declare const ERR_TX_SEND_TIMEOUT = 431;
export declare const ERR_TX_BLOCK_TIMEOUT = 432;
export declare const ERR_TX_SIGNING = 433;
export declare const ERR_TX_GAS_MISMATCH = 434;
export declare const ERR_TX_CHAIN_MISMATCH = 435;
export declare const ERR_TX_HARDFORK_MISMATCH = 436;
export declare const ERR_TX_INVALID_RECEIVER = 437;
export declare const ERR_TX_REVERT_TRANSACTION_CUSTOM_ERROR = 438;
export declare const ERR_TX_INVALID_PROPERTIES_FOR_TYPE = 439;
export declare const ERR_TX_MISSING_GAS_INNER_ERROR = 440;
export declare const ERR_TX_GAS_MISMATCH_INNER_ERROR = 441;
export declare const ERR_CONN = 500;
export declare const ERR_CONN_INVALID = 501;
export declare const ERR_CONN_TIMEOUT = 502;
export declare const ERR_CONN_NOT_OPEN = 503;
export declare const ERR_CONN_CLOSE = 504;
export declare const ERR_CONN_MAX_ATTEMPTS = 505;
export declare const ERR_CONN_PENDING_REQUESTS = 506;
export declare const ERR_REQ_ALREADY_SENT = 507;
export declare const ERR_PROVIDER = 600;
export declare const ERR_INVALID_PROVIDER = 601;
export declare const ERR_INVALID_CLIENT = 602;
export declare const ERR_SUBSCRIPTION = 603;
export declare const ERR_WS_PROVIDER = 604;
export declare const ERR_PRIVATE_KEY_LENGTH = 701;
export declare const ERR_INVALID_PRIVATE_KEY = 702;
export declare const ERR_UNSUPPORTED_KDF = 703;
export declare const ERR_KEY_DERIVATION_FAIL = 704;
export declare const ERR_KEY_VERSION_UNSUPPORTED = 705;
export declare const ERR_INVALID_PASSWORD = 706;
export declare const ERR_IV_LENGTH = 707;
export declare const ERR_INVALID_KEYSTORE = 708;
export declare const ERR_PBKDF2_ITERATIONS = 709;
export declare const ERR_SIGNATURE_FAILED = 801;
export declare const ERR_INVALID_SIGNATURE = 802;
export declare const GENESIS_BLOCK_NUMBER = "0x0";
export declare const JSONRPC_ERR_REJECTED_REQUEST = 4001;
export declare const JSONRPC_ERR_UNAUTHORIZED = 4100;
export declare const JSONRPC_ERR_UNSUPPORTED_METHOD = 4200;
export declare const JSONRPC_ERR_DISCONNECTED = 4900;
export declare const JSONRPC_ERR_CHAIN_DISCONNECTED = 4901;
export declare const ERR_ENS_CHECK_INTERFACE_SUPPORT = 901;
export declare const ERR_ENS_UNSUPPORTED_NETWORK = 902;
export declare const ERR_ENS_NETWORK_NOT_SYNCED = 903;
export declare const ERR_INVALID_STRING = 1001;
export declare const ERR_INVALID_BYTES = 1002;
export declare const ERR_INVALID_NUMBER = 1003;
export declare const ERR_INVALID_UNIT = 1004;
export declare const ERR_INVALID_ADDRESS = 1005;
export declare const ERR_INVALID_HEX = 1006;
export declare const ERR_INVALID_TYPE = 1007;
export declare const ERR_INVALID_BOOLEAN = 1008;
export declare const ERR_INVALID_UNSIGNED_INTEGER = 1009;
export declare const ERR_INVALID_SIZE = 1010;
export declare const ERR_INVALID_LARGE_VALUE = 1011;
export declare const ERR_INVALID_BLOCK = 1012;
export declare const ERR_INVALID_TYPE_ABI = 1013;
export declare const ERR_INVALID_NIBBLE_WIDTH = 1014;
export declare const ERR_INVALID_INTEGER = 1015;
export declare const ERR_VALIDATION = 1100;
export declare const ERR_CORE_HARDFORK_MISMATCH = 1101;
export declare const ERR_CORE_CHAIN_MISMATCH = 1102;
export declare const ERR_SCHEMA_FORMAT = 1200;
export declare const ERR_RPC_INVALID_JSON = -32700;
export declare const ERR_RPC_INVALID_REQUEST = -32600;
export declare const ERR_RPC_INVALID_METHOD = -32601;
export declare const ERR_RPC_INVALID_PARAMS = -32602;
export declare const ERR_RPC_INTERNAL_ERROR = -32603;
export declare const ERR_RPC_INVALID_INPUT = -32000;
export declare const ERR_RPC_MISSING_RESOURCE = -32001;
export declare const ERR_RPC_UNAVAILABLE_RESOURCE = -32002;
export declare const ERR_RPC_TRANSACTION_REJECTED = -32003;
export declare const ERR_RPC_UNSUPPORTED_METHOD = -32004;
export declare const ERR_RPC_LIMIT_EXCEEDED = -32005;
export declare const ERR_RPC_NOT_SUPPORTED = -32006;
