{"version": 3, "file": "scope.js", "sourceRoot": "", "sources": ["../src/scope.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Breadcrumb } from './breadcrumb';\nimport { Context, Contexts } from './context';\nimport { EventProcessor } from './eventprocessor';\nimport { Extra, Extras } from './extra';\nimport { Primitive } from './misc';\nimport { Session } from './session';\nimport { Severity } from './severity';\nimport { Span } from './span';\nimport { Transaction } from './transaction';\nimport { User } from './user';\n\n/** JSDocs */\nexport type CaptureContext = Scope | Partial<ScopeContext> | ((scope: Scope) => Scope);\n\n/** JSDocs */\nexport interface ScopeContext {\n  user: User;\n  level: Severity;\n  extra: Extras;\n  contexts: Contexts;\n  tags: { [key: string]: Primitive };\n  fingerprint: string[];\n}\n\n/**\n * Holds additional event information. {@link Scope.applyToEvent} will be\n * called by the client before an event will be sent.\n */\nexport interface Scope {\n  /** Add new event processor that will be called after {@link applyToEvent}. */\n  addEventProcessor(callback: EventProcessor): this;\n\n  /**\n   * Updates user context information for future events.\n   *\n   * @param user User context object to be set in the current context. Pass `null` to unset the user.\n   */\n  setUser(user: User | null): this;\n\n  /**\n   * Returns the `User` if there is one\n   */\n  getUser(): User | undefined;\n\n  /**\n   * Set an object that will be merged sent as tags data with the event.\n   * @param tags Tags context object to merge into current context.\n   */\n  setTags(tags: { [key: string]: Primitive }): this;\n\n  /**\n   * Set key:value that will be sent as tags data with the event.\n   *\n   * Can also be used to unset a tag by passing `undefined`.\n   *\n   * @param key String key of tag\n   * @param value Value of tag\n   */\n  setTag(key: string, value: Primitive): this;\n\n  /**\n   * Set an object that will be merged sent as extra data with the event.\n   * @param extras Extras object to merge into current context.\n   */\n  setExtras(extras: Extras): this;\n\n  /**\n   * Set key:value that will be sent as extra data with the event.\n   * @param key String of extra\n   * @param extra Any kind of data. This data will be normalized.\n   */\n  setExtra(key: string, extra: Extra): this;\n\n  /**\n   * Sets the fingerprint on the scope to send with the events.\n   * @param fingerprint string[] to group events in Sentry.\n   */\n  setFingerprint(fingerprint: string[]): this;\n\n  /**\n   * Sets the level on the scope for future events.\n   * @param level string {@link Severity}\n   */\n  setLevel(level: Severity): this;\n\n  /**\n   * Sets the transaction name on the scope for future events.\n   */\n  setTransactionName(name?: string): this;\n\n  /**\n   * Sets context data with the given name.\n   * @param name of the context\n   * @param context an object containing context data. This data will be normalized. Pass `null` to unset the context.\n   */\n  setContext(name: string, context: Context | null): this;\n\n  /**\n   * Sets the Span on the scope.\n   * @param span Span\n   */\n  setSpan(span?: Span): this;\n\n  /**\n   * Returns the `Span` if there is one\n   */\n  getSpan(): Span | undefined;\n\n  /**\n   * Returns the `Transaction` attached to the scope (if there is one)\n   */\n  getTransaction(): Transaction | undefined;\n\n  /**\n   * Sets the `Session` on the scope\n   */\n  setSession(session?: Session): this;\n\n  /**\n   * Returns the `Session` if there is one\n   */\n  getSession(): Session | undefined;\n\n  /**\n   * Updates the scope with provided data. Can work in three variations:\n   * - plain object containing updatable attributes\n   * - Scope instance that'll extract the attributes from\n   * - callback function that'll receive the current scope as an argument and allow for modifications\n   * @param captureContext scope modifier to be used\n   */\n  update(captureContext?: CaptureContext): this;\n\n  /** Clears the current scope and resets its properties. */\n  clear(): this;\n\n  /**\n   * Sets the breadcrumbs in the scope\n   * @param breadcrumbs Breadcrumb\n   * @param maxBreadcrumbs number of max breadcrumbs to merged into event.\n   */\n  addBreadcrumb(breadcrumb: Breadcrumb, maxBreadcrumbs?: number): this;\n\n  /**\n   * Clears all currently set Breadcrumbs.\n   */\n  clearBreadcrumbs(): this;\n}\n"]}