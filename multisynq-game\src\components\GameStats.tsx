import React, { useState, useEffect } from 'react'
import './GameStats.css'
import type { Player, GameState } from '../App'

interface GameStatsProps {
  players: Player[]
  gameState: GameState
}

const GameStats: React.FC<GameStatsProps> = ({ players, gameState }) => {
  const [totalBattles, setTotalBattles] = useState(0)
  const [totalRewards, setTotalRewards] = useState(0)

  useEffect(() => {
    // Update stats based on players
    const totalWins = players.reduce((sum, player) => sum + player.wins, 0)
    const totalLosses = players.reduce((sum, player) => sum + player.losses, 0)
    setTotalBattles(totalWins + totalLosses)
    setTotalRewards(totalWins * 0.5) // Mock reward calculation: 0.5 MON per win
  }, [players])

  useEffect(() => {
    // Session timer
    const timer = setInterval(() => {
      setSessionTime(prev => prev + 1)
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const alivePlayers = players.filter(p => p.isAlive).length

  const stats = [
    {
      icon: '⚔️',
      label: 'Active Warriors',
      value: alivePlayers.toString(),
      color: '#4ecdc4'
    },
    {
      icon: '🏆',
      label: 'Total Battles',
      value: totalBattles.toString(),
      color: '#ff6b35'
    },
    {
      icon: '💎',
      label: 'Rewards Pool',
      value: `${totalRewards.toFixed(1)} MON`,
      color: '#ffd700'
    },
    {
      icon: '🎮',
      label: 'Match Status',
      value: gameState.status === 'lobby' ? 'Waiting' :
             gameState.status === 'playing' ? 'Battle!' : 'Finished',
      color: gameState.status === 'playing' ? '#ff6b35' : '#45b7d1'
    }
  ]

  return (
    <div className="game-stats">
      <h3 className="stats-title">📊 Arena Statistics</h3>
      
      <div className="stats-grid">
        {stats.map((stat, index) => (
          <div key={index} className="stat-item">
            <div className="stat-icon" style={{ color: stat.color }}>
              {stat.icon}
            </div>
            <div className="stat-content">
              <div className="stat-value" style={{ color: stat.color }}>
                {stat.value}
              </div>
              <div className="stat-label">
                {stat.label}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="stats-footer">
        <div className="network-info">
          <div className="network-status">
            <div className="status-dot"></div>
            <span>Connected to Monad Testnet</span>
          </div>
          <div className="sync-status">
            <div className="sync-indicator"></div>
            <span>Multisynq Active</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default GameStats
