{"version": 3, "file": "index.bundle.js", "sourceRoot": "", "sources": ["../src/index.bundle.ts"], "names": [], "mappings": ";AAAA,OAAO,EAOL,QAAQ,EAGR,MAAM,GAGP,MAAM,eAAe,CAAC;AAEvB,OAAO,EACL,uBAAuB,EACvB,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,cAAc,EACd,cAAc,EACd,iBAAiB,EACjB,aAAa,EACb,GAAG,EACH,KAAK,EACL,UAAU,EACV,QAAQ,EACR,SAAS,EACT,MAAM,EACN,OAAO,EACP,OAAO,EACP,gBAAgB,EAChB,UAAU,EACV,SAAS,GACV,MAAM,iBAAiB,CAAC;AAGzB,OAAO,EAAE,aAAa,EAAuB,MAAM,iBAAiB,CAAC;AACrE,OAAO,EACL,mBAAmB,EACnB,SAAS,EACT,IAAI,EACJ,WAAW,EACX,MAAM,EACN,gBAAgB,EAChB,KAAK,EACL,KAAK,EACL,IAAI,GACL,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAExD,OAAO,EAAE,YAAY,IAAI,mBAAmB,EAAE,MAAM,iBAAiB,CAAC;AACtE,OAAO,EAAE,eAAe,EAAE,MAAM,eAAe,CAAC;AAEhD,OAAO,EAAE,cAAc,EAAE,MAAM,WAAW,CAAC;AAC3C,OAAO,EAAE,mBAAmB,EAAE,MAAM,iBAAiB,CAAC;AAEtD,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAE9B,IAAI,kBAAkB,GAAG,EAAE,CAAC;AAE5B,gGAAgG;AAChG,IAAM,OAAO,GAAG,eAAe,EAAU,CAAC;AAC1C,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE;IACjD,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC;CAClD;AAED,IAAM,YAAY,kCACb,kBAAkB,GAClB,mBAAmB,KACtB,cAAc,gBAAA,GACf,CAAC;AAEF,OAAO,EAAE,YAAY,IAAI,YAAY,EAAE,CAAC;AAExC,mEAAmE;AACnE,mBAAmB,EAAE,CAAC;AAEtB,OAAO,EAAE,mBAAmB,EAAE,CAAC", "sourcesContent": ["export {\n  Breadcrumb,\n  Request,\n  SdkInfo,\n  Event,\n  Exception,\n  Response,\n  Severity,\n  StackFrame,\n  Stacktrace,\n  Status,\n  Thread,\n  User,\n} from '@sentry/types';\n\nexport {\n  addGlobalEventProcessor,\n  addBreadcrumb,\n  captureException,\n  captureEvent,\n  captureMessage,\n  configureScope,\n  getHubFromCarrier,\n  getCurrentHub,\n  Hub,\n  Scope,\n  setContext,\n  setExtra,\n  setExtras,\n  setTag,\n  setTags,\n  setUser,\n  startTransaction,\n  Transports,\n  withScope,\n} from '@sentry/browser';\n\nexport { BrowserOptions } from '@sentry/browser';\nexport { BrowserClient, ReportDialogOptions } from '@sentry/browser';\nexport {\n  defaultIntegrations,\n  forceLoad,\n  init,\n  lastEventId,\n  onLoad,\n  showReportDialog,\n  flush,\n  close,\n  wrap,\n} from '@sentry/browser';\nexport { SDK_NAME, SDK_VERSION } from '@sentry/browser';\n\nimport { Integrations as BrowserIntegrations } from '@sentry/browser';\nimport { getGlobalObject } from '@sentry/utils';\n\nimport { BrowserTracing } from './browser';\nimport { addExtensionMethods } from './hubextensions';\n\nexport { Span } from './span';\n\nlet windowIntegrations = {};\n\n// This block is needed to add compatibility with the integrations packages when used with a CDN\nconst _window = getGlobalObject<Window>();\nif (_window.Sentry && _window.Sentry.Integrations) {\n  windowIntegrations = _window.Sentry.Integrations;\n}\n\nconst INTEGRATIONS = {\n  ...windowIntegrations,\n  ...BrowserIntegrations,\n  BrowserTracing,\n};\n\nexport { INTEGRATIONS as Integrations };\n\n// We are patching the global object with our hub extension methods\naddExtensionMethods();\n\nexport { addExtensionMethods };\n"]}