'use strict';

/**
 * A JavaScript implementation of the Secure Hash Algorithm, SHA-256, as defined
 * in FIPS 180-2
 * Version 2.2-beta Copyright <PERSON>, <PERSON> 2000 - 2009.
 * Other contributors: <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>inet
 *
 */

var inherits = require('inherits');
var Hash = require('./hash');
var Buffer = require('safe-buffer').Buffer;

var K = [
	0x428A2F98,
	0x71374491,
	0xB5C0FBCF,
	0xE9B5DBA5,
	0x3956C25B,
	0x59F111F1,
	0x923F82A4,
	0xAB1C5ED5,
	0xD807AA98,
	0x12835B01,
	0x243185BE,
	0x550C7DC3,
	0x72BE5D74,
	0x80DEB1FE,
	0x9BDC06A7,
	0xC19BF174,
	0xE49B69C1,
	0xEFBE4786,
	0x0FC19DC6,
	0x240CA1<PERSON>,
	0x2DE92C6F,
	0x4A7484<PERSON>,
	0x5CB0A9DC,
	0x76F988DA,
	0x983E5152,
	0xA831C66D,
	0xB00327C8,
	0xBF597FC7,
	0xC6E00BF3,
	0xD5A79147,
	0x06CA6351,
	0x14292967,
	0x27B70A85,
	0x2E1B2138,
	0x4D2C6DFC,
	0x53380D13,
	0x650A7354,
	0x766A0ABB,
	0x81C2C92E,
	0x92722C85,
	0xA2BFE8A1,
	0xA81A664B,
	0xC24B8B70,
	0xC76C51A3,
	0xD192E819,
	0xD6990624,
	0xF40E3585,
	0x106AA070,
	0x19A4C116,
	0x1E376C08,
	0x2748774C,
	0x34B0BCB5,
	0x391C0CB3,
	0x4ED8AA4A,
	0x5B9CCA4F,
	0x682E6FF3,
	0x748F82EE,
	0x78A5636F,
	0x84C87814,
	0x8CC70208,
	0x90BEFFFA,
	0xA4506CEB,
	0xBEF9A3F7,
	0xC67178F2
];

var W = new Array(64);

function Sha256() {
	this.init();

	this._w = W; // new Array(64)

	Hash.call(this, 64, 56);
}

inherits(Sha256, Hash);

Sha256.prototype.init = function () {
	this._a = 0x6a09e667;
	this._b = 0xbb67ae85;
	this._c = 0x3c6ef372;
	this._d = 0xa54ff53a;
	this._e = 0x510e527f;
	this._f = 0x9b05688c;
	this._g = 0x1f83d9ab;
	this._h = 0x5be0cd19;

	return this;
};

function ch(x, y, z) {
	return z ^ (x & (y ^ z));
}

function maj(x, y, z) {
	return (x & y) | (z & (x | y));
}

function sigma0(x) {
	return ((x >>> 2) | (x << 30)) ^ ((x >>> 13) | (x << 19)) ^ ((x >>> 22) | (x << 10));
}

function sigma1(x) {
	return ((x >>> 6) | (x << 26)) ^ ((x >>> 11) | (x << 21)) ^ ((x >>> 25) | (x << 7));
}

function gamma0(x) {
	return ((x >>> 7) | (x << 25)) ^ ((x >>> 18) | (x << 14)) ^ (x >>> 3);
}

function gamma1(x) {
	return ((x >>> 17) | (x << 15)) ^ ((x >>> 19) | (x << 13)) ^ (x >>> 10);
}

Sha256.prototype._update = function (M) {
	var w = this._w;

	var a = this._a | 0;
	var b = this._b | 0;
	var c = this._c | 0;
	var d = this._d | 0;
	var e = this._e | 0;
	var f = this._f | 0;
	var g = this._g | 0;
	var h = this._h | 0;

	for (var i = 0; i < 16; ++i) {
		w[i] = M.readInt32BE(i * 4);
	}
	for (; i < 64; ++i) {
		w[i] = (gamma1(w[i - 2]) + w[i - 7] + gamma0(w[i - 15]) + w[i - 16]) | 0;
	}

	for (var j = 0; j < 64; ++j) {
		var T1 = (h + sigma1(e) + ch(e, f, g) + K[j] + w[j]) | 0;
		var T2 = (sigma0(a) + maj(a, b, c)) | 0;

		h = g;
		g = f;
		f = e;
		e = (d + T1) | 0;
		d = c;
		c = b;
		b = a;
		a = (T1 + T2) | 0;
	}

	this._a = (a + this._a) | 0;
	this._b = (b + this._b) | 0;
	this._c = (c + this._c) | 0;
	this._d = (d + this._d) | 0;
	this._e = (e + this._e) | 0;
	this._f = (f + this._f) | 0;
	this._g = (g + this._g) | 0;
	this._h = (h + this._h) | 0;
};

Sha256.prototype._hash = function () {
	var H = Buffer.allocUnsafe(32);

	H.writeInt32BE(this._a, 0);
	H.writeInt32BE(this._b, 4);
	H.writeInt32BE(this._c, 8);
	H.writeInt32BE(this._d, 12);
	H.writeInt32BE(this._e, 16);
	H.writeInt32BE(this._f, 20);
	H.writeInt32BE(this._g, 24);
	H.writeInt32BE(this._h, 28);

	return H;
};

module.exports = Sha256;
