import React, { useState, useEffect } from 'react'
import { ethers } from 'ethers'
import './WalletConnection.css'

interface WalletConnectionProps {
  isConnected: boolean
  walletAddress: string
  onConnect: (connected: boolean) => void
  onAddressChange: (address: string) => void
}

const WalletConnection: React.FC<WalletConnectionProps> = ({
  isConnected,
  walletAddress,
  onConnect,
  onAddressChange
}) => {
  const [isConnecting, setIsConnecting] = useState(false)
  const [balance, setBalance] = useState<string>('0')
  const [networkName, setNetworkName] = useState<string>('')

  // Monad Testnet configuration
  const MONAD_TESTNET = {
    chainId: '0x29A', // 666 in hex
    chainName: 'Monad Testnet',
    nativeCurrency: {
      name: 'M<PERSON>',
      symbol: 'MON',
      decimals: 18
    },
    rpcUrls: ['https://rpc.testnet.monad.xyz'],
    blockExplorerUrls: ['https://explorer.testnet.monad.xyz']
  }

  useEffect(() => {
    checkConnection()
  }, [])

  useEffect(() => {
    if (isConnected && walletAddress) {
      getBalance()
      getNetwork()
    }
  }, [isConnected, walletAddress])

  const checkConnection = async () => {
    if (typeof window.ethereum !== 'undefined') {
      try {
        const accounts = await window.ethereum.request({ method: 'eth_accounts' })
        if (accounts.length > 0) {
          onConnect(true)
          onAddressChange(accounts[0])
        }
      } catch (error) {
        console.error('Error checking connection:', error)
      }
    }
  }

  const connectWallet = async () => {
    if (typeof window.ethereum === 'undefined') {
      alert('Please install MetaMask to connect your wallet!')
      return
    }

    setIsConnecting(true)

    try {
      // Request account access
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      })

      if (accounts.length > 0) {
        onConnect(true)
        onAddressChange(accounts[0])

        // Try to switch to Monad Testnet
        await switchToMonadTestnet()
      }
    } catch (error: any) {
      console.error('Error connecting wallet:', error)
      if (error.code === 4001) {
        alert('Please connect to MetaMask.')
      } else {
        alert('Error connecting wallet. Please try again.')
      }
    } finally {
      setIsConnecting(false)
    }
  }

  const switchToMonadTestnet = async () => {
    if (!window.ethereum) return

    try {
      // Try to switch to Monad Testnet
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: MONAD_TESTNET.chainId }]
      })
    } catch (switchError: any) {
      // If the chain doesn't exist, add it
      if (switchError.code === 4902) {
        try {
          await window.ethereum!.request({
            method: 'wallet_addEthereumChain',
            params: [MONAD_TESTNET]
          })
        } catch (addError) {
          console.error('Error adding Monad Testnet:', addError)
        }
      } else {
        console.error('Error switching to Monad Testnet:', switchError)
      }
    }
  }

  const disconnectWallet = () => {
    onConnect(false)
    onAddressChange('')
    setBalance('0')
    setNetworkName('')
  }

  const getBalance = async () => {
    if (!walletAddress || !window.ethereum) return

    try {
      const provider = new ethers.BrowserProvider(window.ethereum)
      const balance = await provider.getBalance(walletAddress)
      setBalance(ethers.formatEther(balance))
    } catch (error) {
      console.error('Error getting balance:', error)
    }
  }

  const getNetwork = async () => {
    if (!window.ethereum) return

    try {
      const provider = new ethers.BrowserProvider(window.ethereum)
      const network = await provider.getNetwork()
      setNetworkName(network.name === 'unknown' ? 'Monad Testnet' : network.name)
    } catch (error) {
      console.error('Error getting network:', error)
    }
  }

  const formatAddress = (address: string) => {
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`
  }

  if (isConnected) {
    return (
      <div className="wallet-connected">
        <div className="wallet-info">
          <div className="wallet-address">
            <span className="address-label">Address:</span>
            <span className="address-value">{formatAddress(walletAddress)}</span>
          </div>
          <div className="wallet-balance">
            <span className="balance-label">Balance:</span>
            <span className="balance-value">{parseFloat(balance).toFixed(4)} MON</span>
          </div>
          {networkName && (
            <div className="wallet-network">
              <span className="network-label">Network:</span>
              <span className="network-value">{networkName}</span>
            </div>
          )}
        </div>
        <button onClick={disconnectWallet} className="btn btn-disconnect">
          Disconnect
        </button>
      </div>
    )
  }

  return (
    <div className="wallet-disconnected">
      <button 
        onClick={connectWallet} 
        disabled={isConnecting}
        className="btn btn-connect"
      >
        {isConnecting ? 'Connecting...' : 'Connect Wallet'}
      </button>
      <p className="wallet-hint">
        Connect your MetaMask wallet to start playing
      </p>
    </div>
  )
}

export default WalletConnection
