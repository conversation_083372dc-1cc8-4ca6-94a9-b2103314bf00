import { SocketService } from '../../../SocketService';
/**
 * Returns a handler function to handle the 'KEY_INFO' event.
 * This handler emits the KEY_INFO event with the provided event data.
 *
 * @param instance The current instance of the SocketService.
 * @param {any} event The event data for the 'KEY_INFO' event.
 * @returns {Function} A handler function for the 'KEY_INFO' event.
 */
export declare function handleKeyInfo(instance: SocketService): (event: any) => void;
//# sourceMappingURL=handleKeyInfo.d.ts.map