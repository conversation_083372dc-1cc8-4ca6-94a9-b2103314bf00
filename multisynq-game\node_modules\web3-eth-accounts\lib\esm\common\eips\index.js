/*
This file is part of web3.js.

web3.js is free software: you can redistribute it and/or modify
it under the terms of the GNU Lesser General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

web3.js is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
ME<PERSON><PERSON>NTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU Lesser General Public License for more details.

You should have received a copy of the GNU Lesser General Public License
along with web3.js.  If not, see <http://www.gnu.org/licenses/>.
*/
import e1153 from './1153.js';
import e1559 from './1559.js';
import e2315 from './2315.js';
import e2537 from './2537.js';
import e2565 from './2565.js';
import e2718 from './2718.js';
import e2929 from './2929.js';
import e2930 from './2930.js';
import e3198 from './3198.js';
import e3529 from './3529.js';
import e3540 from './3540.js';
import e3541 from './3541.js';
import e3554 from './3554.js';
import e3607 from './3607.js';
import e3651 from './3651.js';
import e3670 from './3670.js';
import e3675 from './3675.js';
import e3855 from './3855.js';
import e3860 from './3860.js';
import e4345 from './4345.js';
import e4399 from './4399.js';
import e5133 from './5133.js';
export const EIPs = {
    1153: e1153,
    1559: e1559,
    2315: e2315,
    2537: e2537,
    2565: e2565,
    2718: e2718,
    2929: e2929,
    2930: e2930,
    3198: e3198,
    3529: e3529,
    3540: e3540,
    3541: e3541,
    3554: e3554,
    3607: e3607,
    3651: e3651,
    3670: e3670,
    3675: e3675,
    3855: e3855,
    3860: e3860,
    4345: e4345,
    4399: e4399,
    5133: e5133,
};
//# sourceMappingURL=index.js.map