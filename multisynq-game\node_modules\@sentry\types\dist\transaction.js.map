{"version": 3, "file": "transaction.js", "sourceRoot": "", "sources": ["../src/transaction.ts"], "names": [], "mappings": ";AAuGA,IAAY,yBAKX;AALD,WAAY,yBAAyB;IACnC,wDAA2B,CAAA;IAC3B,uDAA0B,CAAA;IAC1B,iDAAoB,CAAA;IACpB,wDAA2B,CAAA;AAC7B,CAAC,EALW,yBAAyB,GAAzB,iCAAyB,KAAzB,iCAAyB,QAKpC", "sourcesContent": ["import { ExtractedNodeRequestData, Primitive, WorkerLocation } from './misc';\nimport { Span, SpanContext } from './span';\n\n/**\n * Interface holding Transaction-specific properties\n */\nexport interface TransactionContext extends SpanContext {\n  /**\n   * Human-readable identifier for the transaction\n   */\n  name: string;\n\n  /**\n   * If true, sets the end timestamp of the transaction to the highest timestamp of child spans, trimming\n   * the duration of the transaction. This is useful to discard extra time in the transaction that is not\n   * accounted for in child spans, like what happens in the idle transaction Tracing integration, where we finish the\n   * transaction after a given \"idle time\" and we don't want this \"idle time\" to be part of the transaction.\n   */\n  trimEnd?: boolean;\n\n  /**\n   * If this transaction has a parent, the parent's sampling decision\n   */\n  parentSampled?: boolean;\n}\n\n/**\n * Data pulled from a `sentry-trace` header\n */\nexport type TraceparentData = Pick<TransactionContext, 'traceId' | 'parentSpanId' | 'parentSampled'>;\n\n/**\n * Transaction \"Class\", inherits Span only has `setName`\n */\nexport interface Transaction extends TransactionContext, Span {\n  /**\n   * @inheritDoc\n   */\n  spanId: string;\n\n  /**\n   * @inheritDoc\n   */\n  traceId: string;\n\n  /**\n   * @inheritDoc\n   */\n  startTimestamp: number;\n\n  /**\n   * @inheritDoc\n   */\n  tags: { [key: string]: Primitive };\n\n  /**\n   * @inheritDoc\n   */\n  data: { [key: string]: any };\n\n  /**\n   * Set the name of the transaction\n   */\n  setName(name: string): void;\n}\n\n/**\n * Context data passed by the user when starting a transaction, to be used by the tracesSampler method.\n */\nexport interface CustomSamplingContext {\n  [key: string]: any;\n}\n\n/**\n * Data passed to the `tracesSampler` function, which forms the basis for whatever decisions it might make.\n *\n * Adds default data to data provided by the user. See {@link Hub.startTransaction}\n */\nexport interface SamplingContext extends CustomSamplingContext {\n  /**\n   * Context data with which transaction being sampled was created\n   */\n  transactionContext: TransactionContext;\n\n  /**\n   * Sampling decision from the parent transaction, if any.\n   */\n  parentSampled?: boolean;\n\n  /**\n   * Object representing the URL of the current page or worker script. Passed by default in a browser or service worker\n   * context.\n   */\n  location?: WorkerLocation;\n\n  /**\n   * Object representing the incoming request to a node server. Passed by default when using the TracingHandler.\n   */\n  request?: ExtractedNodeRequestData;\n}\n\nexport type Measurements = Record<string, { value: number }>;\n\nexport enum TransactionSamplingMethod {\n  Explicit = 'explicitly_set',\n  Sampler = 'client_sampler',\n  Rate = 'client_rate',\n  Inheritance = 'inheritance',\n}\n"]}