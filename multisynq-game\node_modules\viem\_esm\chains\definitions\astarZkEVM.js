import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
export const astarZkEVM = /*#__PURE__*/ defineChain({
    id: 3_776,
    name: 'Astar zkEVM',
    network: 'AstarZkEVM',
    nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc-zkevm.astar.network'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Astar zkEVM Explorer',
            url: 'https://astar-zkevm.explorer.startale.com',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 93528,
        },
    },
    testnet: false,
});
//# sourceMappingURL=astarZkEVM.js.map