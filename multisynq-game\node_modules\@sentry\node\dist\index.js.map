{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;AAAA,uCAeuB;AANrB,2BAAA,QAAQ,CAAA;AAGR,yBAAA,MAAM,CAAA;AAKR,qCAoBsB;AAnBpB,yCAAA,uBAAuB,CAAA;AACvB,+BAAA,aAAa,CAAA;AACb,kCAAA,gBAAgB,CAAA;AAChB,8BAAA,YAAY,CAAA;AACZ,gCAAA,cAAc,CAAA;AACd,gCAAA,cAAc,CAAA;AACd,mCAAA,iBAAiB,CAAA;AACjB,+BAAA,aAAa,CAAA;AACb,qBAAA,GAAG,CAAA;AACH,0BAAA,QAAQ,CAAA;AACR,uBAAA,KAAK,CAAA;AACL,kCAAA,gBAAgB,CAAA;AAChB,4BAAA,UAAU,CAAA;AACV,0BAAA,QAAQ,CAAA;AACR,2BAAA,SAAS,CAAA;AACT,wBAAA,MAAM,CAAA;AACN,yBAAA,OAAO,CAAA;AACP,yBAAA,OAAO,CAAA;AACP,2BAAA,SAAS,CAAA;AAGX,qCAAqD;AAA5C,gCAAA,WAAW,CAAA;AACpB,mCAAsC;AAA7B,8BAAA,UAAU,CAAA;AACnB,6BAA6E;AAApE,oCAAA,mBAAmB,CAAA;AAAE,qBAAA,IAAI,CAAA;AAAE,4BAAA,WAAW,CAAA;AAAE,sBAAA,KAAK,CAAA;AAAE,sBAAA,KAAK,CAAA;AAC7D,qCAAkD;AAAzC,6BAAA,QAAQ,CAAA;AAAE,gCAAA,WAAW,CAAA;AAE9B,qCAAgE;AAChE,mCAA6C;AAC7C,+BAAiC;AAEjC,qCAAuC;AASY,4BAAQ;AAR3D,iDAAmD;AACnD,yCAA2C;AAOJ,gCAAU;AALjD,IAAM,YAAY,yCACb,mBAAgB,GAChB,gBAAgB,CACpB,CAAC;AAEuB,oCAAY;AAErC,mHAAmH;AACnH,uGAAuG;AACvG,IAAM,OAAO,GAAG,oBAAc,EAAE,CAAC;AACjC,IAAI,OAAO,CAAC,UAAU,EAAE;IACtB,OAAO,CAAC,UAAU,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,UAAU,IAAI,EAAE,CAAC;IACpE,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,IAAI,MAAM,CAAC;CACvF", "sourcesContent": ["export {\n  Breadcrumb,\n  BreadcrumbHint,\n  Request,\n  SdkInfo,\n  Event,\n  EventHint,\n  Exception,\n  Response,\n  Severity,\n  StackFrame,\n  Stacktrace,\n  Status,\n  Thread,\n  User,\n} from '@sentry/types';\n\nexport {\n  addGlobalEventProcessor,\n  addBreadcrumb,\n  captureException,\n  captureEvent,\n  captureMessage,\n  configureScope,\n  getHubFromCarrier,\n  getCurrentHub,\n  Hub,\n  makeMain,\n  Scope,\n  startTransaction,\n  setContext,\n  setExtra,\n  setExtras,\n  setTag,\n  setTags,\n  setUser,\n  withScope,\n} from '@sentry/core';\n\nexport { NodeBackend, NodeOptions } from './backend';\nexport { NodeClient } from './client';\nexport { defaultIntegrations, init, lastEventId, flush, close } from './sdk';\nexport { SDK_NAME, SDK_VERSION } from './version';\n\nimport { Integrations as CoreIntegrations } from '@sentry/core';\nimport { getMainCarrier } from '@sentry/hub';\nimport * as domain from 'domain';\n\nimport * as Handlers from './handlers';\nimport * as NodeIntegrations from './integrations';\nimport * as Transports from './transports';\n\nconst INTEGRATIONS = {\n  ...CoreIntegrations,\n  ...NodeIntegrations,\n};\n\nexport { INTEGRATIONS as Integrations, Transports, Handlers };\n\n// We need to patch domain on the global __SENTRY__ object to make it work for node in cross-platform packages like\n// @sentry/hub. If we don't do this, browser bundlers will have troubles resolving `require('domain')`.\nconst carrier = getMainCarrier();\nif (carrier.__SENTRY__) {\n  carrier.__SENTRY__.extensions = carrier.__SENTRY__.extensions || {};\n  carrier.__SENTRY__.extensions.domain = carrier.__SENTRY__.extensions.domain || domain;\n}\n"]}