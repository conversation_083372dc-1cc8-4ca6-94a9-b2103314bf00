{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../src/utils.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAO,MAAM,aAAa,CAAC;AAGjD,MAAM,CAAC,IAAM,kBAAkB,GAAG,IAAI,MAAM,CAC1C,UAAU,GAAG,aAAa;IAC1B,iBAAiB,GAAG,WAAW;IAC/B,mBAAmB,GAAG,UAAU;IAChC,WAAW,GAAG,UAAU;IACtB,UAAU,CACb,CAAC;AAEF;;;;GAIG;AACH,MAAM,UAAU,iBAAiB,CAAC,OAAgB;IAChD,OAAO,kBAAkB,IAAI,OAAO,IAAI,eAAe,IAAI,OAAO,CAAC;AACrE,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,sBAAsB,CAAC,WAAmB;IACxD,IAAM,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;IACtD,IAAI,OAAO,EAAE;QACX,IAAI,aAAa,SAAqB,CAAC;QACvC,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACtB,aAAa,GAAG,IAAI,CAAC;SACtB;aAAM,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAC7B,aAAa,GAAG,KAAK,CAAC;SACvB;QACD,OAAO;YACL,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;YACnB,aAAa,eAAA;YACb,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC;SACzB,CAAC;KACH;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,iDAAiD;AACjD,MAAM,UAAU,oBAAoB,CAAwB,GAA0B;IAA1B,oBAAA,EAAA,MAAW,aAAa,EAAE;;IACpF,OAAO,YAAA,GAAG,0CAAE,QAAQ,4CAAI,cAAc,EAAmB,CAAC;AAC5D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,IAAY;IAClC,OAAO,IAAI,GAAG,IAAI,CAAC;AACrB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,IAAY;IAClC,OAAO,IAAI,GAAG,IAAI,CAAC;AACrB,CAAC;AAED,uGAAuG;AACvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,eAAe,CAAC", "sourcesContent": ["import { getCurrentH<PERSON>, Hub } from '@sentry/hub';\nimport { Options, TraceparentData, Transaction } from '@sentry/types';\n\nexport const TRACEPARENT_REGEXP = new RegExp(\n  '^[ \\\\t]*' + // whitespace\n  '([0-9a-f]{32})?' + // trace_id\n  '-?([0-9a-f]{16})?' + // span_id\n  '-?([01])?' + // sampled\n    '[ \\\\t]*$', // whitespace\n);\n\n/**\n * Determines if tracing is currently enabled.\n *\n * Tracing is enabled when at least one of `tracesSampleRate` and `tracesSampler` is defined in the SDK config.\n */\nexport function hasTracingEnabled(options: Options): boolean {\n  return 'tracesSampleRate' in options || 'tracesSampler' in options;\n}\n\n/**\n * Extract transaction context data from a `sentry-trace` header.\n *\n * @param traceparent Traceparent string\n *\n * @returns Object containing data from the header, or undefined if traceparent string is malformed\n */\nexport function extractTraceparentData(traceparent: string): TraceparentData | undefined {\n  const matches = traceparent.match(TRACEPARENT_REGEXP);\n  if (matches) {\n    let parentSampled: boolean | undefined;\n    if (matches[3] === '1') {\n      parentSampled = true;\n    } else if (matches[3] === '0') {\n      parentSampled = false;\n    }\n    return {\n      traceId: matches[1],\n      parentSampled,\n      parentSpanId: matches[2],\n    };\n  }\n  return undefined;\n}\n\n/** Grabs active transaction off scope, if any */\nexport function getActiveTransaction<T extends Transaction>(hub: Hub = getCurrentHub()): T | undefined {\n  return hub?.getScope()?.getTransaction() as T | undefined;\n}\n\n/**\n * Converts from milliseconds to seconds\n * @param time time in ms\n */\nexport function msToSec(time: number): number {\n  return time / 1000;\n}\n\n/**\n * Converts from seconds to milliseconds\n * @param time time in seconds\n */\nexport function secToMs(time: number): number {\n  return time * 1000;\n}\n\n// so it can be used in manual instrumentation without necessitating a hard dependency on @sentry/utils\nexport { stripUrlQueryAndFragment } from '@sentry/utils';\n"]}