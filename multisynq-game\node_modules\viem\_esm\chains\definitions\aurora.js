import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const aurora = /*#__PURE__*/ define<PERSON>hain({
    id: 1313161554,
    name: '<PERSON>',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: 'ETH',
    },
    rpcUrls: {
        default: { http: ['https://mainnet.aurora.dev'] },
    },
    blockExplorers: {
        default: {
            name: 'Aurorascan',
            url: 'https://aurorascan.dev',
            apiUrl: 'https://aurorascan.dev/api',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 62907816,
        },
    },
});
//# sourceMappingURL=aurora.js.map