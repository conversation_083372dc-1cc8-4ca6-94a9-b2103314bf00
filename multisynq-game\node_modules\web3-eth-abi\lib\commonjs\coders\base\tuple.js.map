{"version": 3, "file": "tuple.js", "sourceRoot": "", "sources": ["../../../../src/coders/base/tuple.ts"], "names": [], "mappings": ";;AA0BA,kCAkDC;AAED,kCAmDC;AAjID;;;;;;;;;;;;;;;EAeE;AACF,6CAAuC;AAEvC,2CAA8C;AAE9C,2CAA2C;AAC3C,yCAAsF;AACtF,yCAAiD;AACjD,0CAAwC;AACxC,2CAA2C;AAE3C,SAAgB,WAAW,CAAC,KAAmB,EAAE,KAAc;;IAC9D,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QACxD,MAAM,IAAI,sBAAQ,CAAC,sCAAsC,EAAE;YAC1D,KAAK;YACL,KAAK;SACL,CAAC,CAAC;IACJ,CAAC;IACD,MAAM,aAAa,GAAG,KAAiD,CAAC;IACxE,MAAM,OAAO,GAAyB,EAAE,CAAC;IACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAA,MAAA,KAAK,CAAC,UAAU,0CAAE,MAAM,mCAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAC7D,oEAAoE;QACpE,MAAM,cAAc,GAAG,KAAK,CAAC,UAAW,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAI,MAAqB,CAAC;QAC1B,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,IAAI,aAAa,CAAC,MAAM,EAAE,CAAC;gBAC/B,MAAM,IAAI,sBAAQ,CAAC,8BAA8B,EAAE;oBAClD,KAAK;oBACL,KAAK;iBACL,CAAC,CAAC;YACJ,CAAC;YACD,MAAM,GAAG,IAAA,sCAA2B,EAAC,cAAc,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,CAAC;aAAM,CAAC;YACP,MAAM,UAAU,GAAG,aAAa,CAAC,MAAA,cAAc,CAAC,IAAI,mCAAI,EAAE,CAAC,CAAC;YAC5D,2CAA2C;YAC3C,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;gBACrD,MAAM,IAAI,sBAAQ,CAAC,8BAA8B,EAAE;oBAClD,KAAK;oBACL,KAAK;oBACL,SAAS,EAAE,cAAc,CAAC,IAAI;iBAC9B,CAAC,CAAC;YACJ,CAAC;YACD,MAAM,GAAG,IAAA,sCAA2B,EAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAClE,CAAC;QACD,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO,GAAG,IAAI,CAAC;QAChB,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;IAED,IAAI,OAAO,EAAE,CAAC;QACb,OAAO;YACN,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,IAAA,8BAAmB,EAAC,OAAO,CAAC;SACrC,CAAC;IACH,CAAC;IACD,OAAO;QACN,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,IAAA,6BAAgB,EAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;KACzD,CAAC;AACH,CAAC;AAED,SAAgB,WAAW,CAC1B,KAAmB,EACnB,KAAiB;IAEjB,MAAM,MAAM,GAAmD;QAC9D,UAAU,EAAE,CAAC;KACb,CAAC;IAEF,+CAA+C;IAC/C,IAAI,QAAQ,GAAG,CAAC,CAAC;IAEjB,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;QACvB,OAAO;YACN,MAAM;YACN,OAAO,EAAE,KAAK;YACd,QAAQ;SACR,CAAC;IACH,CAAC;IACD,+CAA+C;IAC/C,IAAI,eAAe,GAAG,CAAC,CAAC;IACxB,KAAK,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC;QAC9D,IAAI,aAA4B,CAAC;QACjC,IAAI,IAAA,oBAAS,EAAC,UAAU,CAAC,EAAE,CAAC;YAC3B,0CAA0C;YAC1C,MAAM,YAAY,GAAG,IAAA,wBAAY,EAChC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,EAC5B,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CACxB,CAAC;YACF,qDAAqD;YACrD,aAAa,GAAG,IAAA,sCAA2B,EAC1C,UAAU,EACV,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAC3C,CAAC;YACF,QAAQ,IAAI,YAAY,CAAC,QAAQ,CAAC;YAClC,eAAe,IAAI,aAAa,CAAC,QAAQ,CAAC;QAC3C,CAAC;aAAM,CAAC;YACP,4BAA4B;YAC5B,aAAa,GAAG,IAAA,sCAA2B,EAAC,UAAU,EAAE,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;YAClF,QAAQ,IAAI,aAAa,CAAC,QAAQ,CAAC;QACpC,CAAC;QACD,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC;QACvB,MAAM,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC;QACrC,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;YAC/C,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC;QAChD,CAAC;IACF,CAAC;IACD,OAAO;QACN,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,QAAQ,GAAG,eAAe,CAAC;QACnD,MAAM;QACN,QAAQ,EAAE,QAAQ,GAAG,eAAe;KACpC,CAAC;AACH,CAAC"}