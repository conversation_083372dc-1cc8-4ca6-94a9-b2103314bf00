{"version": 3, "file": "console.js", "sourceRoot": "", "sources": ["../../src/integrations/console.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAC7C,OAAO,EAAe,QAAQ,EAAE,MAAM,eAAe,CAAC;AACtD,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AACrC,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAE7B,iCAAiC;AACjC;IAAA;QAME;;WAEG;QACI,SAAI,GAAW,OAAO,CAAC,EAAE,CAAC;IAWnC,CAAC;IATC;;OAEG;IACI,2BAAS,GAAhB;;QACE,IAAM,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;;YACzC,KAAoB,IAAA,KAAA,SAAA,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA,gBAAA,4BAAE;gBAA1D,IAAM,KAAK,WAAA;gBACd,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;aACzD;;;;;;;;;IACH,CAAC;IAlBD;;OAEG;IACW,UAAE,GAAW,SAAS,CAAC;IAgBvC,cAAC;CAAA,AApBD,IAoBC;SApBY,OAAO;AAsBpB;;GAEG;AACH,SAAS,oBAAoB,CAAC,KAAa;IACzC,OAAO,SAAS,cAAc,CAAC,qBAAiC;QAC9D,IAAI,WAAqB,CAAC;QAE1B,QAAQ,KAAK,EAAE;YACb,KAAK,OAAO;gBACV,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC;gBAC7B,MAAM;YACR,KAAK,OAAO;gBACV,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC;gBAC7B,MAAM;YACR,KAAK,MAAM;gBACT,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC;gBAC5B,MAAM;YACR,KAAK,MAAM;gBACT,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC;gBAC/B,MAAM;YACR;gBACE,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC;SAC9B;QAED,OAAO;YACL,IAAI,aAAa,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;gBAC3C,aAAa,EAAE,CAAC,aAAa,CAC3B;oBACE,QAAQ,EAAE,SAAS;oBACnB,KAAK,EAAE,WAAW;oBAClB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC;iBACjD,EACD;oBACE,KAAK,WAAM,SAAS,CAAC;oBACrB,KAAK,OAAA;iBACN,CACF,CAAC;aACH;YAED,qBAAqB,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAC/C,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { getCurrentHub } from '@sentry/core';\nimport { Integration, Severity } from '@sentry/types';\nimport { fill } from '@sentry/utils';\nimport * as util from 'util';\n\n/** Console module integration */\nexport class Console implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'Console';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = Console.id;\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(): void {\n    const consoleModule = require('console');\n    for (const level of ['debug', 'info', 'warn', 'error', 'log']) {\n      fill(consoleModule, level, createConsoleWrapper(level));\n    }\n  }\n}\n\n/**\n * Wrapper function that'll be used for every console level\n */\nfunction createConsoleWrapper(level: string): (originalConsoleMethod: () => void) => void {\n  return function consoleWrapper(originalConsoleMethod: () => void): () => void {\n    let sentryLevel: Severity;\n\n    switch (level) {\n      case 'debug':\n        sentryLevel = Severity.Debug;\n        break;\n      case 'error':\n        sentryLevel = Severity.Error;\n        break;\n      case 'info':\n        sentryLevel = Severity.Info;\n        break;\n      case 'warn':\n        sentryLevel = Severity.Warning;\n        break;\n      default:\n        sentryLevel = Severity.Log;\n    }\n\n    return function(this: typeof console): void {\n      if (getCurrentHub().getIntegration(Console)) {\n        getCurrentHub().addBreadcrumb(\n          {\n            category: 'console',\n            level: sentryLevel,\n            message: util.format.apply(undefined, arguments),\n          },\n          {\n            input: [...arguments],\n            level,\n          },\n        );\n      }\n\n      originalConsoleMethod.apply(this, arguments);\n    };\n  };\n}\n"]}