import {
  EMITTED_NOTIFICATIONS,
  NOOP,
  getDefaultExternalMiddleware,
  getRpcPromiseCallback,
  isValidChainId,
  isValidNetworkVersion
} from "./chunk-ZN7WV55J.mjs";
import "./chunk-5FL6VRJJ.mjs";
import "./chunk-I6HXGZRD.mjs";
import "./chunk-ZGDQ3IYD.mjs";
import "./chunk-X66SUIEF.mjs";
export {
  EMITTED_NOTIFICATIONS,
  NOOP,
  getDefaultExternalMiddleware,
  getRpcPromiseCallback,
  isValidChainId,
  isValidNetworkVersion
};
//# sourceMappingURL=utils.mjs.map