/* tslint:disable */
/* eslint-disable */

/* auto-generated by NAPI-RS */

/** An account that needs to be created during the genesis block. */
export interface GenesisAccount {
  /** Account secret key */
  secretKey: string
  /** Account balance */
  balance: bigint
}
export interface BlockOptions {
  /** The parent block's hash */
  parentHash?: Buffer
  /** The block's beneficiary */
  beneficiary?: Buffer
  /** The state's root hash */
  stateRoot?: Buffer
  /** The block's difficulty */
  difficulty?: bigint
  /** The block's number */
  number?: bigint
  /** The block's gas limit */
  gasLimit?: bigint
  /** The block's timestamp */
  timestamp?: bigint
  /** The block's extra data */
  extraData?: Buffer
  /** The block's mix hash (or prevrandao) */
  mixHash?: Buffer
  /** The block's nonce */
  nonce?: Buffer
  /** The block's base gas fee */
  baseFee?: bigint
  /** The block's withdrawals */
  withdrawals?: Array<Withdrawal>
  /** Blob gas was added by EIP-4844 and is ignored in older headers. */
  blobGas?: BlobGas
  /**
   * The hash tree root of the parent beacon block for the given execution
   * block (EIP-4788).
   */
  parentBeaconBlockRoot?: Buffer
  /**
   * The commitment hash calculated for a list of [EIP-7685] data requests.
   *
   * [EIP-7685]: https://eips.ethereum.org/EIPS/eip-7685
   */
  requestsHash?: Buffer
}
/** Information about the blob gas used in a block. */
export interface BlobGas {
  /**
   * The total amount of blob gas consumed by the transactions within the
   * block.
   */
  gasUsed: bigint
  /**
   * The running total of blob gas consumed in excess of the target, prior to
   * the block. Blocks with above-target blob gas consumption increase this
   * value, blocks with below-target blob gas consumption decrease it
   * (bounded at 0).
   */
  excessGas: bigint
}
/** The result of executing a call override. */
export interface CallOverrideResult {
  result: Buffer
  shouldRevert: boolean
}
/** Identifier for the Ethereum spec. */
export enum SpecId {
  /** Frontier */
  Frontier = 0,
  /** Frontier Thawing */
  FrontierThawing = 1,
  /** Homestead */
  Homestead = 2,
  /** DAO Fork */
  DaoFork = 3,
  /** Tangerine */
  Tangerine = 4,
  /** Spurious Dragon */
  SpuriousDragon = 5,
  /** Byzantium */
  Byzantium = 6,
  /** Constantinople */
  Constantinople = 7,
  /** Petersburg */
  Petersburg = 8,
  /** Istanbul */
  Istanbul = 9,
  /** Muir Glacier */
  MuirGlacier = 10,
  /** Berlin */
  Berlin = 11,
  /** London */
  London = 12,
  /** Arrow Glacier */
  ArrowGlacier = 13,
  /** Gray Glacier */
  GrayGlacier = 14,
  /** Merge */
  Merge = 15,
  /** Shanghai */
  Shanghai = 16,
  /** Cancun */
  Cancun = 17,
  /** Prague */
  Prague = 18,
  /** Latest */
  Latest = 19
}
export interface DebugTraceResult {
  pass: boolean
  gasUsed: bigint
  output?: Buffer
  structLogs: Array<DebugTraceLogItem>
}
export interface DebugTraceLogItem {
  /** Program Counter */
  pc: bigint
  op: number
  /** Gas left before executing this operation as hex number. */
  gas: string
  /** Gas cost of this operation as hex number. */
  gasCost: string
  /** Array of all values (hex numbers) on the stack */
  stack?: Array<string>
  /** Depth of the call stack */
  depth: bigint
  /** Size of memory array */
  memSize: bigint
  /** Name of the operation */
  opName: string
  /** Description of an error as a hex string. */
  error?: string
  /** Array of all allocated values as hex strings. */
  memory?: Array<string>
  /** Map of all stored values with keys and values encoded as hex strings. */
  storage?: Record<string, string>
}
/** Ethereum execution log. */
export interface ExecutionLog {
  address: Buffer
  topics: Array<Buffer>
  data: Buffer
}
export interface ContractAndFunctionName {
  /** The contract name. */
  contractName: string
  /** The function name. Only present for calls. */
  functionName?: string
}
export interface LoggerConfig {
  /** Whether to enable the logger. */
  enable: boolean
  decodeConsoleLogInputsCallback: (inputs: Buffer[]) => string[]
  printLineCallback: (message: string, replace: boolean) => void
}
/** Configuration for a chain */
export interface ChainConfig {
  /** The chain ID */
  chainId: bigint
  /** The chain's supported hardforks */
  hardforks: Array<HardforkActivation>
}
/** Configuration for forking a blockchain */
export interface ForkConfig {
  /** The URL of the JSON-RPC endpoint to fork from */
  jsonRpcUrl: string
  /**
   * The block number to fork from. If not provided, the latest safe block is
   * used.
   */
  blockNumber?: bigint
  /** The HTTP headers to use when making requests to the JSON-RPC endpoint */
  httpHeaders?: Array<HttpHeader>
}
export interface HttpHeader {
  name: string
  value: string
}
/** Configuration for a hardfork activation */
export interface HardforkActivation {
  /** The block number at which the hardfork is activated */
  blockNumber: bigint
  /** The activated hardfork */
  specId: SpecId
}
/**The type of ordering to use when selecting blocks to mine. */
export enum MineOrdering {
  /**Insertion order */
  Fifo = 'Fifo',
  /**Effective miner fee */
  Priority = 'Priority'
}
/** Configuration for the provider's mempool. */
export interface MemPoolConfig {
  order: MineOrdering
}
export interface IntervalRange {
  min: bigint
  max: bigint
}
/** Configuration for the provider's miner. */
export interface MiningConfig {
  autoMine: boolean
  interval?: bigint | IntervalRange
  memPool: MemPoolConfig
}
/** Configuration for a provider */
export interface ProviderConfig {
  /** Whether to allow blocks with the same timestamp */
  allowBlocksWithSameTimestamp: boolean
  /** Whether to allow unlimited contract size */
  allowUnlimitedContractSize: boolean
  /** Whether to return an `Err` when `eth_call` fails */
  bailOnCallFailure: boolean
  /** Whether to return an `Err` when a `eth_sendTransaction` fails */
  bailOnTransactionFailure: boolean
  /** The gas limit of each block */
  blockGasLimit: bigint
  /** The directory to cache remote JSON-RPC responses */
  cacheDir?: string
  /** The chain ID of the blockchain */
  chainId: bigint
  /** The configuration for chains */
  chains: Array<ChainConfig>
  /** The address of the coinbase */
  coinbase: Buffer
  /** Enables RIP-7212 */
  enableRip7212: boolean
  /**
   * The configuration for forking a blockchain. If not provided, a local
   * blockchain will be created
   */
  fork?: ForkConfig
  /** The genesis accounts of the blockchain */
  genesisAccounts: Array<GenesisAccount>
  /** The hardfork of the blockchain */
  hardfork: SpecId
  /**
   * The initial base fee per gas of the blockchain. Required for EIP-1559
   * transactions and later
   */
  initialBaseFeePerGas?: bigint
  /** The initial blob gas of the blockchain. Required for EIP-4844 */
  initialBlobGas?: BlobGas
  /** The initial date of the blockchain, in seconds since the Unix epoch */
  initialDate?: bigint
  /**
   * The initial parent beacon block root of the blockchain. Required for
   * EIP-4788
   */
  initialParentBeaconBlockRoot?: Buffer
  /** The minimum gas price of the next block. */
  minGasPrice: bigint
  /** The configuration for the miner */
  mining: MiningConfig
  /** The network ID of the blockchain */
  networkId: bigint
}
/** Tracing config for Solidity stack trace generation. */
export interface TracingConfigWithBuffers {
  /**
   * Build information to use for decoding contracts. Either a Hardhat v2
   * build info file that contains both input and output or a Hardhat v3
   * build info file that doesn't contain output and a separate output file.
   */
  buildInfos?: Array<Uint8Array> | Array<BuildInfoAndOutput>
  /** Whether to ignore contracts whose name starts with "Ignored". */
  ignoreContracts?: boolean
}
/**
 * Hardhat V3 build info where the compiler output is not part of the build
 * info file.
 */
export interface BuildInfoAndOutput {
  /** The build info input file */
  buildInfo: Uint8Array
  /** The build info output file */
  output: Uint8Array
}
/** The possible reasons for successful termination of the EVM. */
export enum SuccessReason {
  /** The opcode `STOP` was called */
  Stop = 0,
  /** The opcode `RETURN` was called */
  Return = 1,
  /** The opcode `SELFDESTRUCT` was called */
  SelfDestruct = 2,
  EofReturnContract = 3
}
export interface CallOutput {
  /** Return value */
  returnValue: Buffer
}
export interface CreateOutput {
  /** Return value */
  returnValue: Buffer
  /** Optionally, a 160-bit address */
  address?: Buffer
}
/** The result when the EVM terminates successfully. */
export interface SuccessResult {
  /** The reason for termination */
  reason: SuccessReason
  /** The amount of gas used */
  gasUsed: bigint
  /** The amount of gas refunded */
  gasRefunded: bigint
  /** The logs */
  logs: Array<ExecutionLog>
  /** The transaction output */
  output: CallOutput | CreateOutput
}
/** The result when the EVM terminates due to a revert. */
export interface RevertResult {
  /** The amount of gas used */
  gasUsed: bigint
  /** The transaction output */
  output: Buffer
}
/**
 * Indicates that the EVM has experienced an exceptional halt. This causes
 * execution to immediately end with all gas being consumed.
 */
export enum ExceptionalHalt {
  OutOfGas = 0,
  OpcodeNotFound = 1,
  InvalidFEOpcode = 2,
  InvalidJump = 3,
  NotActivated = 4,
  StackUnderflow = 5,
  StackOverflow = 6,
  OutOfOffset = 7,
  CreateCollision = 8,
  PrecompileError = 9,
  NonceOverflow = 10,
  /** Create init code size exceeds limit (runtime). */
  CreateContractSizeLimit = 11,
  /** Error on created contract that begins with EF */
  CreateContractStartingWithEF = 12,
  /** EIP-3860: Limit and meter initcode. Initcode size limit exceeded. */
  CreateInitCodeSizeLimit = 13,
  /** Aux data overflow, new aux data is larger tha u16 max size. */
  EofAuxDataOverflow = 14,
  /** Aud data is smaller then already present data size. */
  EofAuxDataTooSmall = 15,
  /** EOF Subroutine stack overflow */
  EOFFunctionStackOverflow = 16,
  /** Check for target address validity is only done inside subcall. */
  InvalidEXTCALLTarget = 17
}
/** The result when the EVM terminates due to an exceptional halt. */
export interface HaltResult {
  /** The exceptional halt that occurred */
  reason: ExceptionalHalt
  /**
   * Halting will spend all the gas and will thus be equal to the specified
   * gas limit
   */
  gasUsed: bigint
}
/** The result of executing a transaction. */
export interface ExecutionResult {
  /** The transaction result */
  result: SuccessResult | RevertResult | HaltResult
  /** Optional contract address if the transaction created a new contract. */
  contractAddress?: Buffer
}
export interface SubscriptionEvent {
  filterId: bigint
  result: any
}
export declare function linkHexStringBytecode(code: string, address: string, position: number): string
export declare function printStackTrace(trace: SolidityStackTrace): void
/** Represents the exit code of the EVM. */
export enum ExitCode {
  /** Execution was successful. */
  SUCCESS = 0,
  /** Execution was reverted. */
  REVERT = 1,
  /** Execution ran out of gas. */
  OUT_OF_GAS = 2,
  /** Execution encountered an internal error. */
  INTERNAL_ERROR = 3,
  /** Execution encountered an invalid opcode. */
  INVALID_OPCODE = 4,
  /** Execution encountered a stack underflow. */
  STACK_UNDERFLOW = 5,
  /** Create init code size exceeds limit (runtime). */
  CODESIZE_EXCEEDS_MAXIMUM = 6,
  /** Create collision. */
  CREATE_COLLISION = 7,
  /** Unknown halt reason. */
  UNKNOWN_HALT_REASON = 8
}
export enum ContractFunctionType {
  CONSTRUCTOR = 0,
  FUNCTION = 1,
  FALLBACK = 2,
  RECEIVE = 3,
  GETTER = 4,
  MODIFIER = 5,
  FREE_FUNCTION = 6
}
export enum StackTraceEntryType {
  CALLSTACK_ENTRY = 0,
  UNRECOGNIZED_CREATE_CALLSTACK_ENTRY = 1,
  UNRECOGNIZED_CONTRACT_CALLSTACK_ENTRY = 2,
  PRECOMPILE_ERROR = 3,
  REVERT_ERROR = 4,
  PANIC_ERROR = 5,
  CUSTOM_ERROR = 6,
  FUNCTION_NOT_PAYABLE_ERROR = 7,
  INVALID_PARAMS_ERROR = 8,
  FALLBACK_NOT_PAYABLE_ERROR = 9,
  FALLBACK_NOT_PAYABLE_AND_NO_RECEIVE_ERROR = 10,
  UNRECOGNIZED_FUNCTION_WITHOUT_FALLBACK_ERROR = 11,
  MISSING_FALLBACK_OR_RECEIVE_ERROR = 12,
  RETURNDATA_SIZE_ERROR = 13,
  NONCONTRACT_ACCOUNT_CALLED_ERROR = 14,
  CALL_FAILED_ERROR = 15,
  DIRECT_LIBRARY_CALL_ERROR = 16,
  UNRECOGNIZED_CREATE_ERROR = 17,
  UNRECOGNIZED_CONTRACT_ERROR = 18,
  OTHER_EXECUTION_ERROR = 19,
  UNMAPPED_SOLC_0_6_3_REVERT_ERROR = 20,
  CONTRACT_TOO_LARGE_ERROR = 21,
  INTERNAL_FUNCTION_CALLSTACK_ENTRY = 22,
  CONTRACT_CALL_RUN_OUT_OF_GAS_ERROR = 23
}
export declare function stackTraceEntryTypeToString(val: StackTraceEntryType): string
export const FALLBACK_FUNCTION_NAME: string
export const RECEIVE_FUNCTION_NAME: string
export const CONSTRUCTOR_FUNCTION_NAME: string
export const UNRECOGNIZED_FUNCTION_NAME: string
export const UNKNOWN_FUNCTION_NAME: string
export const PRECOMPILE_FUNCTION_NAME: string
export const UNRECOGNIZED_CONTRACT_NAME: string
export interface SourceReference {
  sourceName: string
  sourceContent: string
  contract?: string
  function?: string
  line: number
  range: Array<number>
}
export interface CallstackEntryStackTraceEntry {
  type: StackTraceEntryType.CALLSTACK_ENTRY
  sourceReference: SourceReference
  functionType: ContractFunctionType
}
export interface UnrecognizedCreateCallstackEntryStackTraceEntry {
  type: StackTraceEntryType.UNRECOGNIZED_CREATE_CALLSTACK_ENTRY
  sourceReference?: undefined
}
export interface UnrecognizedContractCallstackEntryStackTraceEntry {
  type: StackTraceEntryType.UNRECOGNIZED_CONTRACT_CALLSTACK_ENTRY
  address: Uint8Array
  sourceReference?: undefined
}
export interface PrecompileErrorStackTraceEntry {
  type: StackTraceEntryType.PRECOMPILE_ERROR
  precompile: number
  sourceReference?: undefined
}
export interface RevertErrorStackTraceEntry {
  type: StackTraceEntryType.REVERT_ERROR
  returnData: Uint8Array
  sourceReference: SourceReference
  isInvalidOpcodeError: boolean
}
export interface PanicErrorStackTraceEntry {
  type: StackTraceEntryType.PANIC_ERROR
  errorCode: bigint
  sourceReference?: SourceReference
}
export interface CustomErrorStackTraceEntry {
  type: StackTraceEntryType.CUSTOM_ERROR
  message: string
  sourceReference: SourceReference
}
export interface FunctionNotPayableErrorStackTraceEntry {
  type: StackTraceEntryType.FUNCTION_NOT_PAYABLE_ERROR
  value: bigint
  sourceReference: SourceReference
}
export interface InvalidParamsErrorStackTraceEntry {
  type: StackTraceEntryType.INVALID_PARAMS_ERROR
  sourceReference: SourceReference
}
export interface FallbackNotPayableErrorStackTraceEntry {
  type: StackTraceEntryType.FALLBACK_NOT_PAYABLE_ERROR
  value: bigint
  sourceReference: SourceReference
}
export interface FallbackNotPayableAndNoReceiveErrorStackTraceEntry {
  type: StackTraceEntryType.FALLBACK_NOT_PAYABLE_AND_NO_RECEIVE_ERROR
  value: bigint
  sourceReference: SourceReference
}
export interface UnrecognizedFunctionWithoutFallbackErrorStackTraceEntry {
  type: StackTraceEntryType.UNRECOGNIZED_FUNCTION_WITHOUT_FALLBACK_ERROR
  sourceReference: SourceReference
}
export interface MissingFallbackOrReceiveErrorStackTraceEntry {
  type: StackTraceEntryType.MISSING_FALLBACK_OR_RECEIVE_ERROR
  sourceReference: SourceReference
}
export interface ReturndataSizeErrorStackTraceEntry {
  type: StackTraceEntryType.RETURNDATA_SIZE_ERROR
  sourceReference: SourceReference
}
export interface NonContractAccountCalledErrorStackTraceEntry {
  type: StackTraceEntryType.NONCONTRACT_ACCOUNT_CALLED_ERROR
  sourceReference: SourceReference
}
export interface CallFailedErrorStackTraceEntry {
  type: StackTraceEntryType.CALL_FAILED_ERROR
  sourceReference: SourceReference
}
export interface DirectLibraryCallErrorStackTraceEntry {
  type: StackTraceEntryType.DIRECT_LIBRARY_CALL_ERROR
  sourceReference: SourceReference
}
export interface UnrecognizedCreateErrorStackTraceEntry {
  type: StackTraceEntryType.UNRECOGNIZED_CREATE_ERROR
  returnData: Uint8Array
  sourceReference?: undefined
  isInvalidOpcodeError: boolean
}
export interface UnrecognizedContractErrorStackTraceEntry {
  type: StackTraceEntryType.UNRECOGNIZED_CONTRACT_ERROR
  address: Uint8Array
  returnData: Uint8Array
  sourceReference?: undefined
  isInvalidOpcodeError: boolean
}
export interface OtherExecutionErrorStackTraceEntry {
  type: StackTraceEntryType.OTHER_EXECUTION_ERROR
  sourceReference?: SourceReference
}
export interface UnmappedSolc063RevertErrorStackTraceEntry {
  type: StackTraceEntryType.UNMAPPED_SOLC_0_6_3_REVERT_ERROR
  sourceReference?: SourceReference
}
export interface ContractTooLargeErrorStackTraceEntry {
  type: StackTraceEntryType.CONTRACT_TOO_LARGE_ERROR
  sourceReference?: SourceReference
}
export interface InternalFunctionCallStackEntry {
  type: StackTraceEntryType.INTERNAL_FUNCTION_CALLSTACK_ENTRY
  pc: number
  sourceReference: SourceReference
}
export interface ContractCallRunOutOfGasError {
  type: StackTraceEntryType.CONTRACT_CALL_RUN_OUT_OF_GAS_ERROR
  sourceReference?: SourceReference
}
export interface TracingMessage {
  /** Sender address */
  readonly caller: Buffer
  /** Recipient address. None if it is a Create message. */
  readonly to?: Buffer
  /** Whether it's a static call */
  readonly isStaticCall: boolean
  /** Transaction gas limit */
  readonly gasLimit: bigint
  /** Depth of the message */
  readonly depth: number
  /** Input data of the message */
  readonly data: Buffer
  /** Value sent in the message */
  readonly value: bigint
  /**
   * Address of the code that is being executed. Can be different from `to`
   * if a delegate call is being done.
   */
  readonly codeAddress?: Buffer
  /** Code of the contract that is being executed. */
  readonly code?: Buffer
}
export interface TracingStep {
  /** Call depth */
  readonly depth: number
  /** The program counter */
  readonly pc: bigint
  /** The executed op code */
  readonly opcode: string
  /**
   * The entries on the stack. It only contains the top element unless
   * verbose tracing is enabled. The vector is empty if there are no elements
   * on the stack.
   */
  readonly stack: Array<bigint>
  /** The memory at the step. None if verbose tracing is disabled. */
  readonly memory?: Buffer
}
export interface TracingMessageResult {
  /** Execution result */
  readonly executionResult: ExecutionResult
}
/**
 * Returns the latest version of solc that EDR officially
 * supports and is tested against.
 */
export declare function getLatestSupportedSolcVersion(): string
export interface Withdrawal {
  /** The index of withdrawal */
  index: bigint
  /** The index of the validator that generated the withdrawal */
  validatorIndex: bigint
  /** The recipient address for withdrawal value */
  address: Buffer
  /** The value contained in withdrawal */
  amount: bigint
}
export declare class EdrContext {
  /**Creates a new [`EdrContext`] instance. Should only be called once! */
  constructor()
}
/** A JSON-RPC provider for Ethereum. */
export declare class Provider {
  /**Constructs a new provider with the provided configuration. */
  static withConfig(context: EdrContext, config: ProviderConfig, loggerConfig: LoggerConfig, tracingConfig: TracingConfigWithBuffers, subscriberCallback: (event: SubscriptionEvent) => void): Promise<Provider>
  /**Handles a JSON-RPC request and returns a JSON-RPC response. */
  handleRequest(jsonRequest: string): Promise<Response>
  setCallOverrideCallback(callOverrideCallback: (contract_address: Buffer, data: Buffer) => Promise<CallOverrideResult | undefined>): void
  /**
   * Set to `true` to make the traces returned with `eth_call`,
   * `eth_estimateGas`, `eth_sendRawTransaction`, `eth_sendTransaction`,
   * `evm_mine`, `hardhat_mine` include the full stack and memory. Set to
   * `false` to disable this.
   */
  setVerboseTracing(verboseTracing: boolean): void
}
export declare class Response {
  /** Returns the response data as a JSON string or a JSON object. */
  get data(): string | any
  get traces(): Array<RawTrace>
  /**Compute the error stack trace. Return the stack trace if it can be decoded, otherwise returns none. Throws if there was an error computing the stack trace. */
  stackTrace(): SolidityStackTrace | null
}
export declare class Exit {
  get kind(): ExitCode
  isError(): boolean
  getReason(): string
}
/**
 * Opaque handle to the `Bytecode` struct.
 * Only used on the JS side by the `VmTraceDecoder` class.
 */
export declare class BytecodeWrapper { }
export declare class ReturnData {
  readonly value: Uint8Array
  constructor(value: Uint8Array)
  isEmpty(): boolean
  isErrorReturnData(): boolean
  isPanicReturnData(): boolean
  decodeError(): string
  decodePanic(): bigint
}
export declare class RawTrace {
  trace(): Array<TracingMessage | TracingStep | TracingMessageResult>
}
