{"version": 3, "file": "postgres.js", "sourceRoot": "", "sources": ["../../src/integrations/postgres.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAQ7D,oDAAoD;AACpD;IAAA;QAME;;WAEG;QACI,SAAI,GAAW,QAAQ,CAAC,EAAE,CAAC;IAoDpC,CAAC;IAlDC;;OAEG;IACI,4BAAS,GAAhB,UAAiB,CAAqC,EAAE,aAAwB;QAC9E,IAAI,MAAgB,CAAC;QAErB,IAAI;YACF,IAAM,QAAQ,GAAG,cAAc,CAAC,MAAM,EAAE,IAAI,CAAyB,CAAC;YACtE,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;SAC1B;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC;YACzE,OAAO;SACR;QAED;;;;;WAKG;QACH,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,UAAS,IAAmC;YAC1E,OAAO,UAAwB,MAAe,EAAE,MAAe,EAAE,QAAiB;;gBAChF,IAAM,KAAK,GAAG,aAAa,EAAE,CAAC,QAAQ,EAAE,CAAC;gBACzC,IAAM,UAAU,SAAG,KAAK,0CAAE,OAAO,EAAE,CAAC;gBACpC,IAAM,IAAI,SAAG,UAAU,0CAAE,UAAU,CAAC;oBAClC,WAAW,EAAE,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAE,MAA2B,CAAC,IAAI;oBACpF,EAAE,EAAE,IAAI;iBACT,CAAC,CAAC;gBAEH,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;oBAClC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,UAAS,GAAU,EAAE,MAAe;;wBACzE,MAAA,IAAI,0CAAE,MAAM,GAAG;wBACf,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;oBACxB,CAAC,CAAC,CAAC;iBACJ;gBAED,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;oBAChC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,UAAS,GAAU,EAAE,MAAe;;wBACjE,MAAA,IAAI,0CAAE,MAAM,GAAG;wBACf,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC;iBACJ;gBAED,OAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAsB,CAAC,IAAI,CAAC,UAAC,GAAY;;oBAC7E,MAAA,IAAI,0CAAE,MAAM,GAAG;oBACf,OAAO,GAAG,CAAC;gBACb,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IA3DD;;OAEG;IACW,WAAE,GAAW,UAAU,CAAC;IAyDxC,eAAC;CAAA,AA7DD,IA6DC;SA7DY,QAAQ", "sourcesContent": ["import { Hub } from '@sentry/hub';\nimport { EventProcessor, Integration } from '@sentry/types';\nimport { dynamicRequire, fill, logger } from '@sentry/utils';\n\ninterface PgClient {\n  prototype: {\n    query: () => void | Promise<unknown>;\n  };\n}\n\n/** Tracing integration for node-postgres package */\nexport class Postgres implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'Postgres';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = Postgres.id;\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(_: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void {\n    let client: PgClient;\n\n    try {\n      const pgModule = dynamicRequire(module, 'pg') as { Client: PgClient };\n      client = pgModule.Client;\n    } catch (e) {\n      logger.error('Postgres Integration was unable to require `pg` package.');\n      return;\n    }\n\n    /**\n     * function (query, callback) => void\n     * function (query, params, callback) => void\n     * function (query) => Promise\n     * function (query, params) => Promise\n     */\n    fill(client.prototype, 'query', function(orig: () => void | Promise<unknown>) {\n      return function(this: unknown, config: unknown, values: unknown, callback: unknown) {\n        const scope = getCurrentHub().getScope();\n        const parentSpan = scope?.getSpan();\n        const span = parentSpan?.startChild({\n          description: typeof config === 'string' ? config : (config as { text: string }).text,\n          op: `db`,\n        });\n\n        if (typeof callback === 'function') {\n          return orig.call(this, config, values, function(err: Error, result: unknown) {\n            span?.finish();\n            callback(err, result);\n          });\n        }\n\n        if (typeof values === 'function') {\n          return orig.call(this, config, function(err: Error, result: unknown) {\n            span?.finish();\n            values(err, result);\n          });\n        }\n\n        return (orig.call(this, config, values) as Promise<unknown>).then((res: unknown) => {\n          span?.finish();\n          return res;\n        });\n      };\n    });\n  }\n}\n"]}