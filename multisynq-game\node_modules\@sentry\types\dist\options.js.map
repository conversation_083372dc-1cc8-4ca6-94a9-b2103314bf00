{"version": 3, "file": "options.js", "sourceRoot": "", "sources": ["../src/options.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Breadcrumb, BreadcrumbHint } from './breadcrumb';\nimport { Event, EventHint } from './event';\nimport { Integration } from './integration';\nimport { LogLevel } from './loglevel';\nimport { SamplingContext } from './transaction';\nimport { Transport, TransportClass, TransportOptions } from './transport';\n\n/** Base configuration options for every SDK. */\nexport interface Options {\n  /**\n   * Enable debug functionality in the SDK itself\n   */\n  debug?: boolean;\n\n  /**\n   * Specifies whether this SDK should activate and send events to Sentry.\n   * Disabling the SDK reduces all overhead from instrumentation, collecting\n   * breadcrumbs and capturing events. Defaults to true.\n   */\n  enabled?: boolean;\n\n  /**\n   * The Dsn used to connect to Sentry and identify the project. If omitted, the\n   * SDK will not send any data to Sentry.\n   */\n  dsn?: string;\n\n  /**\n   * If this is set to false, default integrations will not be added, otherwise this will internally be set to the\n   * recommended default integrations.\n   * TODO: We should consider changing this to `boolean | Integration[]`\n   */\n  defaultIntegrations?: false | Integration[];\n\n  /**\n   * List of integrations that should be installed after SDK was initialized.\n   * Accepts either a list of integrations or a function that receives\n   * default integrations and returns a new, updated list.\n   */\n  integrations?: Integration[] | ((integrations: Integration[]) => Integration[]);\n\n  /**\n   * A pattern for error messages which should not be sent to Sentry.\n   * By default, all errors will be sent.\n   */\n  ignoreErrors?: Array<string | RegExp>;\n\n  /**\n   * Transport object that should be used to send events to Sentry\n   */\n  transport?: TransportClass<Transport>;\n\n  /**\n   * Options for the default transport that the SDK uses.\n   */\n  transportOptions?: TransportOptions;\n\n  /**\n   * The release identifier used when uploading respective source maps. Specify\n   * this value to allow Sentry to resolve the correct source maps when\n   * processing events.\n   */\n  release?: string;\n\n  /** The current environment of your application (e.g. \"production\"). */\n  environment?: string;\n\n  /** Sets the distribution for all events */\n  dist?: string;\n\n  /**\n   * The maximum number of breadcrumbs sent with events. Defaults to 100.\n   * Values over 100 will be ignored and 100 used instead.\n   */\n  maxBreadcrumbs?: number;\n\n  /** Console logging verbosity for the SDK Client. */\n  logLevel?: LogLevel;\n\n  /** A global sample rate to apply to all events (0 - 1). */\n  sampleRate?: number;\n\n  /** Attaches stacktraces to pure capture message / log integrations */\n  attachStacktrace?: boolean;\n\n  /** Maxium number of chars a single value can have before it will be truncated. */\n  maxValueLength?: number;\n\n  /**\n   * Maximum number of levels that normalization algorithm will traverse in objects and arrays.\n   * Used when normalizing an event before sending, on all of the listed attributes:\n   * - `breadcrumbs.data`\n   * - `user`\n   * - `contexts`\n   * - `extra`\n   * Defaults to `3`. Set to `0` to disable.\n   */\n  normalizeDepth?: number;\n\n  /**\n   * Controls how many milliseconds to wait before shutting down. The default is\n   * SDK-specific but typically around 2 seconds. Setting this too low can cause\n   * problems for sending events from command line applications. Setting it too\n   * high can cause the application to block for users with network connectivity\n   * problems.\n   */\n  shutdownTimeout?: number;\n\n  /**\n   * Options which are in beta, or otherwise not guaranteed to be stable.\n   */\n  _experiments?: {\n    [key: string]: any;\n  };\n\n  /**\n   * Sample rate to determine trace sampling.\n   *\n   * 0.0 = 0% chance of a given trace being sent (send no traces) 1.0 = 100% chance of a given trace being sent (send\n   * all traces)\n   *\n   * Tracing is enabled if either this or `tracesSampler` is defined. If both are defined, `tracesSampleRate` is\n   * ignored.\n   */\n  tracesSampleRate?: number;\n\n  /**\n   * Function to compute tracing sample rate dynamically and filter unwanted traces.\n   *\n   * Tracing is enabled if either this or `tracesSampleRate` is defined. If both are defined, `tracesSampleRate` is\n   * ignored.\n   *\n   * Will automatically be passed a context object of default and optional custom data. See\n   * {@link Transaction.samplingContext} and {@link Hub.startTransaction}.\n   *\n   * @returns A sample rate between 0 and 1 (0 drops the trace, 1 guarantees it will be sent). Returning `true` is\n   * equivalent to returning 1 and returning `false` is equivalent to returning 0.\n   */\n  tracesSampler?(samplingContext: SamplingContext): number | boolean;\n\n  /**\n   * A callback invoked during event submission, allowing to optionally modify\n   * the event before it is sent to Sentry.\n   *\n   * Note that you must return a valid event from this callback. If you do not\n   * wish to modify the event, simply return it at the end.\n   * Returning null will cause the event to be dropped.\n   *\n   * @param event The error or message event generated by the SDK.\n   * @param hint May contain additional information about the original exception.\n   * @returns A new event that will be sent | null.\n   */\n  beforeSend?(event: Event, hint?: EventHint): PromiseLike<Event | null> | Event | null;\n\n  /**\n   * A callback invoked when adding a breadcrumb, allowing to optionally modify\n   * it before adding it to future events.\n   *\n   * Note that you must return a valid breadcrumb from this callback. If you do\n   * not wish to modify the breadcrumb, simply return it at the end.\n   * Returning null will cause the breadcrumb to be dropped.\n   *\n   * @param breadcrumb The breadcrumb as created by the SDK.\n   * @returns The breadcrumb that will be added | null.\n   */\n  beforeBreadcrumb?(breadcrumb: Breadcrumb, hint?: BreadcrumbHint): Breadcrumb | null;\n}\n"]}