#!/usr/bin/env node

const rlp = require('../dist/index.js')
const command = process.argv[2]
var raw = process.argv[3]

if (command === 'encode') {
  try {
    const json = JSON.parse(raw)
    console.log(rlp.encode(json).toString('hex'))
  } catch (e) {
    console.log('invalid json')
  }
} else {
  if (!raw) {
    raw = command
  }
  try {
    console.log(baToJSON(rlp.decode(raw)))
  } catch (e) {
    console.log('invalid RLP' + e)
  }
}

function baToJSON (ba) {
  if (Buffer.isBuffer(ba)) {
    return ba.toString('hex')
  } else if (ba instanceof Array) {
    var array = []
    for (var i = 0; i < ba.length; i++) {
      array.push(baToJSON(ba[i]))
    }
    return array
  }
}
