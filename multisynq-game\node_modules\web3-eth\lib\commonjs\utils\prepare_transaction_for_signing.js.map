{"version": 3, "file": "prepare_transaction_for_signing.js", "sourceRoot": "", "sources": ["../../../src/utils/prepare_transaction_for_signing.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;;;;;;;;;;AAEF,2CAUoB;AAEpB,2CAAsC;AACtC,yDAA0E;AAC1E,mDAA2C;AAC3C,oDAAiE;AACjE,mEAA4D;AAC5D,qEAA8D;AAE9D,MAAM,kCAAkC,GAAG,CAC1C,WAA6E,EAC5E,EAAE;;IAAC,OAAA,iCACD,WAAW,KACd,KAAK,EAAE,WAAW,CAAC,KAAK,EACxB,QAAQ,EAAE,WAAW,CAAC,QAAQ,EAC9B,QAAQ,EAAE,MAAA,WAAW,CAAC,QAAQ,mCAAI,WAAW,CAAC,GAAG,EACjD,EAAE,EAAE,WAAW,CAAC,EAAE,EAClB,KAAK,EAAE,WAAW,CAAC,KAAK,EACxB,IAAI,EAAE,MAAA,WAAW,CAAC,IAAI,mCAAI,WAAW,CAAC,KAAK,EAC3C,IAAI,EAAE,WAAW,CAAC,IAAI,EACtB,OAAO,EAAE,WAAW,CAAC,OAAO,EAC5B,UAAU,EACT,WACA,CAAC,UAAU,EACZ,oBAAoB,EACnB,WACA,CAAC,oBAAoB,EACtB,YAAY,EACX,WACA,CAAC,YAAY,IACb,CAAA;CAAA,CAAC;AAEH,MAAM,+BAA+B,GAAG,CACvC,WAA6E,EAC7E,WAAyC,EACxC,EAAE;;IACH,MAAM,4BAA4B,GACjC,CAAC,CAAC,IAAA,0BAAS,EAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,IAAA,0BAAS,EAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACnE,CAAC,IAAA,0BAAS,EAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAEhC,IAAI,MAAM,CAAC;IACX,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACnC,2CAA2C;QAC3C,IAAI,WAAW,CAAC,aAAa,EAAE,CAAC;YAC/B,MAAM,qBAAQ,WAAW,CAAC,aAAa,CAAE,CAAC;YAE1C,IAAI,IAAA,0BAAS,EAAC,MAAM,CAAC,QAAQ,CAAC;gBAC7B,MAAM,CAAC,QAAQ,GAAG,MAAA,WAAW,CAAC,QAAQ,mCAAI,WAAW,CAAC,eAAe,CAAC;YACvE,IAAI,IAAA,0BAAS,EAAC,MAAM,CAAC,SAAS,CAAC;gBAC9B,MAAM,CAAC,SAAS,GAAG,WAAW,CAAC,YAA2B,CAAC;QAC7D,CAAC;aAAM,CAAC;YACP,MAAM,GAAG,0BAAM,CAAC,MAAM,CACrB;gBACC,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,IAAA,qBAAQ,EAAC,WAAW,CAAC,OAAO,CAAW;gBAChD,SAAS,EAAE,CAAC,IAAA,0BAAS,EAAC,WAAW,CAAC,SAAS,CAAC;oBAC3C,CAAC,CAAE,IAAA,qBAAQ,EAAC,WAAW,CAAC,SAAS,CAAY;oBAC7C,CAAC,CAAC,SAAS;gBACZ,eAAe,EAAE,MAAA,WAAW,CAAC,QAAQ,mCAAI,WAAW,CAAC,eAAe;aACpE,EACD;gBACC,SAAS,EAAE,WAAW,CAAC,YAAY;aACnC,CACD,CAAC;QACH,CAAC;IACF,CAAC;SAAM,CAAC;QACP,MAAM,IAAI,GACT,MAAA,MAAA,MAAA,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,MAAM,0CAAE,WAAW,0CAAE,IAAI,mCAAI,WAAW,CAAC,KAAK,mCAAI,gBAAgB,CAAC;QACjF,MAAM,OAAO,GAAG,IAAA,qBAAQ,EACvB,MAAA,MAAA,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,MAAM,0CAAE,WAAW,0CAAE,OAAO,mCAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,OAAO,CACvD,CAAC;QACZ,MAAM,SAAS,GAAG,IAAA,qBAAQ,EACzB,MAAA,MAAA,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,MAAM,0CAAE,WAAW,0CAAE,SAAS,mCAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,SAAS,CAC3D,CAAC;QACZ,MAAM,eAAe,GACpB,MAAA,MAAA,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,MAAM,0CAAE,QAAQ,mCAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,QAAQ,mCAAI,WAAW,CAAC,eAAe,CAAC;QACvF,MAAM,SAAS,GACd,MAAA,MAAA,MAAA,WAAW,CAAC,MAAM,0CAAE,SAAS,mCAAI,WAAW,CAAC,KAAK,mCAAI,WAAW,CAAC,YAAY,CAAC;QAEhF,IAAI,OAAO,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;YAClC,MAAM,GAAG,0BAAM,CAAC,MAAM,CACrB;gBACC,IAAI;gBACJ,OAAO;gBACP,SAAS;gBACT,eAAe;aACf,EACD;gBACC,SAAS;aACT,CACD,CAAC;QACH,CAAC;IACF,CAAC;IACD,OAAO,EAAE,MAAM,EAAe,CAAC;AAChC,CAAC,CAAC;AAEK,MAAM,4BAA4B,GAAG,wDAM1C,EAAE,+FALH,WAAwB,EACxB,WAAyC,EACzC,UAAmC,EACnC,YAAY,GAAG,KAAK,EACpB,YAAY,GAAG,IAAI;IAEnB,MAAM,oBAAoB,GAAG,CAAC,MAAM,IAAA,2CAAkB,EAAC;QACtD,WAAW;QACX,WAAW;QACX,UAAU;QACV,YAAY;QACZ,YAAY;KACZ,CAAC,CAA4C,CAAC;IAC/C,MAAM,oBAAoB,GAAG,IAAA,yCAAiB,EAAC,oBAAoB,EAAE,4BAAe,EAAE;QACrF,iBAAiB,EAAE,WAAW,CAAC,MAAM,CAAC,uBAAuB;KAC7D,CAAgF,CAAC;IAElF,IAAA,6CAA6B,EAC5B,oBAAkF,EAClF,SAAS,EACT;QACC,iBAAiB,EAAE,WAAW,CAAC,MAAM,CAAC,uBAAuB;KAC7D,CACD,CAAC;IAEF,OAAO,sCAAkB,CAAC,UAAU,CACnC,kCAAkC,CAAC,oBAAoB,CAAC,EACxD,+BAA+B,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAClE,CAAC;AACH,CAAC,CAAA,CAAC;AA9BW,QAAA,4BAA4B,gCA8BvC"}