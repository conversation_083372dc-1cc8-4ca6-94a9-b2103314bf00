/** JSDoc */
export declare function resolve(...args: string[]): string;
/** JSDoc */
export declare function relative(from: string, to: string): string;
/** JSDoc */
export declare function normalizePath(path: string): string;
/** JSDoc */
export declare function isAbsolute(path: string): boolean;
/** JSDoc */
export declare function join(...args: string[]): string;
/** JSDoc */
export declare function dirname(path: string): string;
/** JSDoc */
export declare function basename(path: string, ext?: string): string;
//# sourceMappingURL=path.d.ts.map