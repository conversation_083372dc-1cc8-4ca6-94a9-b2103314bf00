import React, { useRef, useEffect, useState, useCallback } from 'react'
import './ArenaGame.css'
import type { GameState } from '../App'

interface ArenaGameProps {
  gameState: GameState
  onGameStateChange: (gameState: GameState) => void
  currentPlayer: string
  gameSession: string
}

const ArenaGame: React.FC<ArenaGameProps> = ({
  gameState,
  onGameStateChange,
  currentPlayer,
  gameSession
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [keys, setKeys] = useState<Set<string>>(new Set())
  const [isShielding, setIsShielding] = useState(false)
  const [lastDash, setLastDash] = useState(0)
  const [lastAttack, setLastAttack] = useState(0)
  const animationFrameRef = useRef<number | undefined>(undefined)

  // Game constants
  const ARENA_WIDTH = 800
  const ARENA_HEIGHT = 600
  const PLAYER_SIZE = 20
  const PLAYER_SPEED = 3
  const DASH_SPEED = 8
  const DASH_COOLDOWN = 1000 // ms
  const ATTACK_COOLDOWN = 500 // ms
  const SHIELD_DURATION = 2000 // ms

  // Game loop
  const gameLoop = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Clear canvas
    ctx.fillStyle = '#0a0a0a'
    ctx.fillRect(0, 0, ARENA_WIDTH, ARENA_HEIGHT)

    // Draw arena boundaries
    ctx.strokeStyle = '#ffd700'
    ctx.lineWidth = 3
    ctx.strokeRect(0, 0, ARENA_WIDTH, ARENA_HEIGHT)

    // Draw grid pattern
    ctx.strokeStyle = 'rgba(255, 215, 0, 0.1)'
    ctx.lineWidth = 1
    for (let x = 0; x < ARENA_WIDTH; x += 50) {
      ctx.beginPath()
      ctx.moveTo(x, 0)
      ctx.lineTo(x, ARENA_HEIGHT)
      ctx.stroke()
    }
    for (let y = 0; y < ARENA_HEIGHT; y += 50) {
      ctx.beginPath()
      ctx.moveTo(0, y)
      ctx.lineTo(ARENA_WIDTH, y)
      ctx.stroke()
    }

    // Draw players
    gameState.players.forEach(player => {
      if (!player.isAlive) return

      const isCurrentPlayer = player.id === currentPlayer

      // Shield effect (if shielding)
      if (isCurrentPlayer && isShielding) {
        ctx.strokeStyle = '#4ecdc4'
        ctx.lineWidth = 4
        ctx.setLineDash([5, 5])
        ctx.beginPath()
        ctx.arc(player.position.x, player.position.y, PLAYER_SIZE + 10, 0, Math.PI * 2)
        ctx.stroke()
        ctx.setLineDash([])
      }

      // Player body
      ctx.fillStyle = isCurrentPlayer ? '#4ecdc4' : '#ff6b35'
      ctx.beginPath()
      ctx.arc(player.position.x, player.position.y, PLAYER_SIZE, 0, Math.PI * 2)
      ctx.fill()

      // Player outline
      ctx.strokeStyle = isCurrentPlayer ? '#ffffff' : '#ffd700'
      ctx.lineWidth = 2
      ctx.stroke()

      // Attack range indicator (for current player)
      if (isCurrentPlayer && Date.now() - lastAttack < 200) {
        ctx.strokeStyle = 'rgba(255, 107, 53, 0.5)'
        ctx.lineWidth = 2
        ctx.beginPath()
        ctx.arc(player.position.x, player.position.y, 60, 0, Math.PI * 2)
        ctx.stroke()
      }

      // Health bar
      const healthBarWidth = 40
      const healthBarHeight = 6
      const healthPercentage = player.health / 100

      ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
      ctx.fillRect(
        player.position.x - healthBarWidth / 2,
        player.position.y - PLAYER_SIZE - 15,
        healthBarWidth,
        healthBarHeight
      )

      ctx.fillStyle = healthPercentage > 0.5 ? '#4ecdc4' : healthPercentage > 0.25 ? '#ffd700' : '#ff6b6b'
      ctx.fillRect(
        player.position.x - healthBarWidth / 2,
        player.position.y - PLAYER_SIZE - 15,
        healthBarWidth * healthPercentage,
        healthBarHeight
      )

      // Player name
      ctx.fillStyle = '#ffffff'
      ctx.font = '12px Arial'
      ctx.textAlign = 'center'
      ctx.fillText(
        `${player.address.substring(0, 6)}...`,
        player.position.x,
        player.position.y + PLAYER_SIZE + 20
      )
    })

    // Continue game loop
    animationFrameRef.current = requestAnimationFrame(gameLoop)
  }, [gameState, currentPlayer])

  // Handle player movement
  const updatePlayerPosition = useCallback(() => {
    if (gameState.status !== 'playing') return

    const currentPlayerData = gameState.players.find(p => p.id === currentPlayer)
    if (!currentPlayerData || !currentPlayerData.isAlive) return

    let newX = currentPlayerData.position.x
    let newY = currentPlayerData.position.y
    let speed = PLAYER_SPEED

    // Check for dash (with cooldown)
    const now = Date.now()
    if (keys.has('Shift') && now - lastDash > DASH_COOLDOWN) {
      speed = DASH_SPEED
      setLastDash(now)
    }

    // Movement
    if (keys.has('w') || keys.has('W') || keys.has('ArrowUp')) {
      newY = Math.max(PLAYER_SIZE, newY - speed)
    }
    if (keys.has('s') || keys.has('S') || keys.has('ArrowDown')) {
      newY = Math.min(ARENA_HEIGHT - PLAYER_SIZE, newY + speed)
    }
    if (keys.has('a') || keys.has('A') || keys.has('ArrowLeft')) {
      newX = Math.max(PLAYER_SIZE, newX - speed)
    }
    if (keys.has('d') || keys.has('D') || keys.has('ArrowRight')) {
      newX = Math.min(ARENA_WIDTH - PLAYER_SIZE, newX + speed)
    }

    // Update player position
    if (newX !== currentPlayerData.position.x || newY !== currentPlayerData.position.y) {
      const updatedPlayers = gameState.players.map(player =>
        player.id === currentPlayer
          ? { ...player, position: { x: newX, y: newY } }
          : player
      )

      onGameStateChange({
        ...gameState,
        players: updatedPlayers
      })

      // TODO: Send position update to Multisynq
      // multisynqClient.sendPlayerUpdate({ playerId: currentPlayer, position: { x: newX, y: newY } })
    }
  }, [gameState, currentPlayer, keys, onGameStateChange])

  // Handle attacks
  const handleAttack = useCallback(() => {
    if (gameState.status !== 'playing') return

    const now = Date.now()
    if (now - lastAttack < ATTACK_COOLDOWN) return

    const attacker = gameState.players.find(p => p.id === currentPlayer)
    if (!attacker || !attacker.isAlive) return

    setLastAttack(now)

    // Find nearby players to attack
    const attackRange = 60
    const damage = 25

    const updatedPlayers = gameState.players.map(player => {
      if (player.id === currentPlayer || !player.isAlive) return player

      const distance = Math.sqrt(
        Math.pow(player.position.x - attacker.position.x, 2) +
        Math.pow(player.position.y - attacker.position.y, 2)
      )

      if (distance <= attackRange) {
        // Check if target is shielding (reduce damage)
        const actualDamage = (player.id === currentPlayer && isShielding) ? damage * 0.3 : damage
        const newHealth = Math.max(0, player.health - actualDamage)
        return {
          ...player,
          health: newHealth,
          isAlive: newHealth > 0
        }
      }

      return player
    })

    onGameStateChange({
      ...gameState,
      players: updatedPlayers
    })

    // TODO: Send attack to Multisynq
    // multisynqClient.sendAttack({ attackerId: currentPlayer, damage, range: attackRange })
  }, [gameState, currentPlayer, onGameStateChange, lastAttack, isShielding])

  // Handle shield
  const handleShield = useCallback((activate: boolean) => {
    setIsShielding(activate)

    if (activate) {
      // Auto-disable shield after duration
      setTimeout(() => setIsShielding(false), SHIELD_DURATION)
    }
  }, [])

  // Initialize canvas and game loop
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    canvas.width = ARENA_WIDTH
    canvas.height = ARENA_HEIGHT

    gameLoop()

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [gameLoop])

  // Movement update loop
  useEffect(() => {
    const interval = setInterval(updatePlayerPosition, 16) // ~60 FPS
    return () => clearInterval(interval)
  }, [updatePlayerPosition])

  // Keyboard event handlers
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      setKeys(prev => new Set(prev).add(e.key))

      if (e.key === ' ') {
        e.preventDefault()
        handleAttack()
      }

      if (e.key === 'e' || e.key === 'E') {
        e.preventDefault()
        handleShield(true)
      }
    }

    const handleKeyUp = (e: KeyboardEvent) => {
      setKeys(prev => {
        const newKeys = new Set(prev)
        newKeys.delete(e.key)
        return newKeys
      })

      if (e.key === 'e' || e.key === 'E') {
        handleShield(false)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    window.addEventListener('keyup', handleKeyUp)

    return () => {
      window.removeEventListener('keydown', handleKeyDown)
      window.removeEventListener('keyup', handleKeyUp)
    }
  }, [handleAttack, handleShield])

  // Timer countdown
  useEffect(() => {
    if (gameState.status !== 'playing') return

    const timer = setInterval(() => {
      onGameStateChange((prev: GameState) => {
        const newTimer = prev.matchTimer - 1

        if (newTimer <= 0) {
          // Game over - find winner
          const alivePlayers = prev.players.filter((p: any) => p.isAlive)
          const winner = alivePlayers.length === 1 ? alivePlayers[0].id : undefined

          return {
            ...prev,
            status: 'finished' as const,
            matchTimer: 0,
            winner
          }
        }

        return {
          ...prev,
          matchTimer: newTimer
        }
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [gameState.status, onGameStateChange])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="arena-game">
      <div className="game-hud">
        <div className="match-timer">
          <span className="timer-label">Time</span>
          <span className="timer-value">{formatTime(gameState.matchTimer)}</span>
        </div>
        
        <div className="game-status">
          {gameState.status === 'finished' && (
            <div className="game-over">
              <h3>🏆 Match Finished!</h3>
              {gameState.winner && (
                <p>Winner: {gameState.winner.substring(0, 10)}...</p>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="arena-container">
        <canvas
          ref={canvasRef}
          className="arena-canvas"
          onMouseMove={(e) => {
            const rect = e.currentTarget.getBoundingClientRect()
            setMousePos({
              x: e.clientX - rect.left,
              y: e.clientY - rect.top
            })
          }}
        />
      </div>

      <div className="controls-hint">
        <span>WASD: Move</span>
        <span>SPACE: Attack</span>
        <span>SHIFT: Dash</span>
        <span>E: Shield</span>
      </div>

      <div className="ability-hud">
        <div className="ability-item">
          <div className="ability-icon">⚔️</div>
          <div className="ability-name">Attack</div>
          <div className={`cooldown-bar ${Date.now() - lastAttack < ATTACK_COOLDOWN ? 'active' : ''}`}>
            <div
              className="cooldown-fill"
              style={{
                width: `${Math.max(0, 100 - ((Date.now() - lastAttack) / ATTACK_COOLDOWN * 100))}%`
              }}
            ></div>
          </div>
        </div>

        <div className="ability-item">
          <div className="ability-icon">💨</div>
          <div className="ability-name">Dash</div>
          <div className={`cooldown-bar ${Date.now() - lastDash < DASH_COOLDOWN ? 'active' : ''}`}>
            <div
              className="cooldown-fill"
              style={{
                width: `${Math.max(0, 100 - ((Date.now() - lastDash) / DASH_COOLDOWN * 100))}%`
              }}
            ></div>
          </div>
        </div>

        <div className="ability-item">
          <div className="ability-icon">🛡️</div>
          <div className="ability-name">Shield</div>
          <div className={`cooldown-bar ${isShielding ? 'active' : ''}`}>
            <div
              className="cooldown-fill shield"
              style={{
                width: isShielding ? '100%' : '0%'
              }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ArenaGame
