{"version": 3, "file": "getFirstHidden.js", "sourceRoot": "", "sources": ["../../../../src/browser/web-vitals/lib/getFirstHidden.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAEtC,IAAI,eAAuB,CAAC;AAM5B,MAAM,CAAC,IAAM,cAAc,GAAG;IAC5B,IAAI,eAAe,KAAK,SAAS,EAAE;QACjC,sEAAsE;QACtE,uEAAuE;QACvE,oEAAoE;QACpE,mBAAmB;QACnB,eAAe,GAAG,QAAQ,CAAC,eAAe,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAEvE,uDAAuD;QACvD,QAAQ,CAAC,UAAC,EAAa;gBAAX,wBAAS;YAAO,OAAA,CAAC,eAAe,GAAG,SAAS,CAAC;QAA7B,CAA6B,EAAE,IAAI,CAAC,CAAC;KAClE;IAED,OAAO;QACL,IAAI,SAAS;YACX,OAAO,eAAe,CAAC;QACzB,CAAC;KACF,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { onHidden } from './onHidden';\n\nlet firstHiddenTime: number;\n\ntype HiddenType = {\n  readonly timeStamp: number;\n};\n\nexport const getFirstHidden = (): HiddenType => {\n  if (firstHiddenTime === undefined) {\n    // If the document is hidden when this code runs, assume it was hidden\n    // since navigation start. This isn't a perfect heuristic, but it's the\n    // best we can do until an API is available to support querying past\n    // visibilityState.\n    firstHiddenTime = document.visibilityState === 'hidden' ? 0 : Infinity;\n\n    // Update the time if/when the document becomes hidden.\n    onHidden(({ timeStamp }) => (firstHiddenTime = timeStamp), true);\n  }\n\n  return {\n    get timeStamp() {\n      return firstHiddenTime;\n    },\n  };\n};\n"]}