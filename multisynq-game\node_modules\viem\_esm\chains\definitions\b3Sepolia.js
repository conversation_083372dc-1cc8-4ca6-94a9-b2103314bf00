import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
const sourceId = 168_587_773; // base-sepolia
export const b3Sepolia = /*#__PURE__*/ define<PERSON>hain({
    id: 1993,
    name: 'B3 Sepolia',
    nativeCurrency: {
        name: '<PERSON><PERSON>',
        symbol: 'ETH',
        decimals: 18,
    },
    rpcUrls: {
        default: {
            http: ['https://sepolia.b3.fun/http'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Blockscout',
            url: 'https://sepolia.explorer.b3.fun',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 0,
        },
    },
    testnet: true,
    sourceId,
});
//# sourceMappingURL=b3Sepolia.js.map