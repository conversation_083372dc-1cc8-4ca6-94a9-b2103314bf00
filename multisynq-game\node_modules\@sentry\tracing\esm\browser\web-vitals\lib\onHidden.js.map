{"version": 3, "file": "onHidden.js", "sourceRoot": "", "sources": ["../../../../src/browser/web-vitals/lib/onHidden.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAOH,IAAI,WAAW,GAAG,KAAK,CAAC;AACxB,IAAI,cAAc,GAAG,KAAK,CAAC;AAE3B,IAAM,UAAU,GAAG,UAAC,KAA0B;IAC5C,WAAW,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC;AACjC,CAAC,CAAC;AAEF,IAAM,YAAY,GAAG;IACnB,gBAAgB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAEzC,4CAA4C;IAC5C,+DAA+D;IAC/D,gEAAgE;IAChE,gBAAgB,CAAC,cAAc,EAAE,cAAO,CAAC,CAAC,CAAC;AAC7C,CAAC,CAAC;AAEF,MAAM,CAAC,IAAM,QAAQ,GAAG,UAAC,EAAoB,EAAE,IAAY;IAAZ,qBAAA,EAAA,YAAY;IACzD,IAAI,CAAC,cAAc,EAAE;QACnB,YAAY,EAAE,CAAC;QACf,cAAc,GAAG,IAAI,CAAC;KACvB;IAED,gBAAgB,CACd,kBAAkB,EAClB,UAAC,EAAa;YAAX,wBAAS;QACV,IAAI,QAAQ,CAAC,eAAe,KAAK,QAAQ,EAAE;YACzC,EAAE,CAAC,EAAE,SAAS,WAAA,EAAE,WAAW,aAAA,EAAE,CAAC,CAAC;SAChC;IACH,CAAC,EACD,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,MAAA,EAAE,CACxB,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface OnHiddenCallback {\n  // TODO(philipwalton): add `isPersisted` if needed for bfcache.\n  ({ timeStamp, isUnloading }: { timeStamp: number; isUnloading: boolean }): void;\n}\n\nlet isUnloading = false;\nlet listenersAdded = false;\n\nconst onPageHide = (event: PageTransitionEvent): void => {\n  isUnloading = !event.persisted;\n};\n\nconst addListeners = (): void => {\n  addEventListener('pagehide', onPageHide);\n\n  // `beforeunload` is needed to fix this bug:\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=987409\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  addEventListener('beforeunload', () => {});\n};\n\nexport const onHidden = (cb: OnHiddenCallback, once = false): void => {\n  if (!listenersAdded) {\n    addListeners();\n    listenersAdded = true;\n  }\n\n  addEventListener(\n    'visibilitychange',\n    ({ timeStamp }) => {\n      if (document.visibilityState === 'hidden') {\n        cb({ timeStamp, isUnloading });\n      }\n    },\n    { capture: true, once },\n  );\n};\n"]}