import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const astarZkyoto = /*#__PURE__*/ defineChain({
    id: 6_038_361,
    name: 'Astar zkEVM Testnet zKyoto',
    network: 'zKyo<PERSON>',
    nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.startale.com/zkyoto'],
        },
    },
    blockExplorers: {
        default: {
            name: 'zKyoto Explorer',
            url: 'https://zkyoto.explorer.startale.com',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 196153,
        },
    },
    testnet: true,
});
//# sourceMappingURL=astarZkyoto.js.map